"""
LSTM Model for Bitcoin Price Prediction and Trading Signals

This module implements LSTM neural networks for time series prediction
using OpenBB-enhanced Bitcoin data with technical indicators.
"""

import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error
from typing import Dict, List, Tuple, Optional, Union
import logging
from dataclasses import dataclass
import joblib
from pathlib import Path
import json

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Set random seeds for reproducibility
tf.random.set_seed(42)
np.random.seed(42)


@dataclass
class LSTMConfig:
    """Configuration for LSTM model"""
    sequence_length: int = 60  # Number of time steps to look back
    features: List[str] = None  # Features to use for training
    target: str = 'close'  # Target variable to predict
    
    # Model architecture
    lstm_units: List[int] = None  # LSTM layer units
    dropout_rate: float = 0.2
    dense_units: List[int] = None  # Dense layer units
    
    # Training parameters
    batch_size: int = 32
    epochs: int = 100
    validation_split: float = 0.2
    early_stopping_patience: int = 10
    learning_rate: float = 0.001
    
    # Prediction parameters
    prediction_horizon: int = 1  # How many steps ahead to predict
    
    def __post_init__(self):
        if self.features is None:
            self.features = [
                'open', 'high', 'low', 'close', 'volume',
                'sma_20', 'sma_50', 'ema_12', 'ema_26',
                'rsi', 'macd', 'macd_signal', 'bb_upper', 'bb_lower', 'atr'
            ]
        if self.lstm_units is None:
            self.lstm_units = [50, 50]
        if self.dense_units is None:
            self.dense_units = [25]


class BitcoinLSTM:
    """
    LSTM model for Bitcoin price prediction and trading signal generation
    """
    
    def __init__(self, config: LSTMConfig):
        self.config = config
        self.model = None
        self.scaler = None
        self.feature_scaler = None
        self.training_history = None
        self.feature_columns = None
        
    def prepare_data(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """
        Prepare data for LSTM training
        
        Returns:
            X_train, X_test, y_train, y_test
        """
        logger.info("Preparing data for LSTM training...")
        
        # Select and validate features
        available_features = [col for col in self.config.features if col in data.columns]
        missing_features = [col for col in self.config.features if col not in data.columns]
        
        if missing_features:
            logger.warning(f"Missing features: {missing_features}")
        
        if not available_features:
            raise ValueError("No valid features found in data")
        
        self.feature_columns = available_features
        logger.info(f"Using {len(self.feature_columns)} features: {self.feature_columns}")
        
        # Prepare feature matrix and target
        feature_data = data[self.feature_columns].copy()
        target_data = data[self.config.target].copy()
        
        # Handle missing values
        feature_data = feature_data.fillna(method='ffill').fillna(method='bfill')
        target_data = target_data.fillna(method='ffill').fillna(method='bfill')
        
        # Scale features and target separately
        self.feature_scaler = StandardScaler()
        self.scaler = MinMaxScaler(feature_range=(0, 1))
        
        scaled_features = self.feature_scaler.fit_transform(feature_data)
        scaled_target = self.scaler.fit_transform(target_data.values.reshape(-1, 1)).flatten()
        
        # Create sequences
        X, y = self._create_sequences(scaled_features, scaled_target)
        
        # Split data (80% train, 20% test)
        split_idx = int(len(X) * 0.8)
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]
        
        logger.info(f"Training data shape: X={X_train.shape}, y={y_train.shape}")
        logger.info(f"Test data shape: X={X_test.shape}, y={y_test.shape}")
        
        return X_train, X_test, y_train, y_test
    
    def _create_sequences(self, features: np.ndarray, target: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Create sequences for LSTM training"""
        X, y = [], []
        
        for i in range(self.config.sequence_length, len(features) - self.config.prediction_horizon + 1):
            # Features: sequence_length time steps of all features
            X.append(features[i - self.config.sequence_length:i])
            # Target: next value(s) to predict
            if self.config.prediction_horizon == 1:
                y.append(target[i])
            else:
                y.append(target[i:i + self.config.prediction_horizon])
        
        return np.array(X), np.array(y)
    
    def build_model(self, input_shape: Tuple[int, int]) -> keras.Model:
        """Build LSTM model architecture"""
        logger.info(f"Building LSTM model with input shape: {input_shape}")
        
        model = keras.Sequential()
        
        # Add LSTM layers
        for i, units in enumerate(self.config.lstm_units):
            return_sequences = i < len(self.config.lstm_units) - 1
            
            if i == 0:
                model.add(layers.LSTM(
                    units,
                    return_sequences=return_sequences,
                    input_shape=input_shape,
                    name=f'lstm_{i+1}'
                ))
            else:
                model.add(layers.LSTM(
                    units,
                    return_sequences=return_sequences,
                    name=f'lstm_{i+1}'
                ))
            
            model.add(layers.Dropout(self.config.dropout_rate, name=f'dropout_lstm_{i+1}'))
        
        # Add dense layers
        for i, units in enumerate(self.config.dense_units):
            model.add(layers.Dense(units, activation='relu', name=f'dense_{i+1}'))
            model.add(layers.Dropout(self.config.dropout_rate, name=f'dropout_dense_{i+1}'))
        
        # Output layer
        output_units = self.config.prediction_horizon
        model.add(layers.Dense(output_units, activation='linear', name='output'))
        
        # Compile model
        optimizer = keras.optimizers.Adam(learning_rate=self.config.learning_rate)
        model.compile(
            optimizer=optimizer,
            loss='mse',
            metrics=['mae']
        )
        
        logger.info(f"Model built with {model.count_params()} parameters")
        return model
    
    def train(self, X_train: np.ndarray, y_train: np.ndarray, 
              X_val: Optional[np.ndarray] = None, y_val: Optional[np.ndarray] = None) -> Dict:
        """Train the LSTM model"""
        logger.info("Starting LSTM model training...")
        
        # Build model
        input_shape = (X_train.shape[1], X_train.shape[2])
        self.model = self.build_model(input_shape)
        
        # Prepare validation data
        if X_val is None or y_val is None:
            validation_data = None
            validation_split = self.config.validation_split
        else:
            validation_data = (X_val, y_val)
            validation_split = 0.0
        
        # Callbacks
        callbacks = [
            keras.callbacks.EarlyStopping(
                monitor='val_loss' if validation_data or validation_split > 0 else 'loss',
                patience=self.config.early_stopping_patience,
                restore_best_weights=True,
                verbose=1
            ),
            keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss' if validation_data or validation_split > 0 else 'loss',
                factor=0.5,
                patience=5,
                min_lr=1e-7,
                verbose=1
            )
        ]
        
        # Train model
        history = self.model.fit(
            X_train, y_train,
            batch_size=self.config.batch_size,
            epochs=self.config.epochs,
            validation_data=validation_data,
            validation_split=validation_split,
            callbacks=callbacks,
            verbose=1
        )
        
        self.training_history = history.history
        
        # Training summary
        final_loss = history.history['loss'][-1]
        final_val_loss = history.history.get('val_loss', [None])[-1]
        
        logger.info(f"Training completed!")
        logger.info(f"Final training loss: {final_loss:.6f}")
        if final_val_loss:
            logger.info(f"Final validation loss: {final_val_loss:.6f}")
        
        return self.training_history
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """Make predictions using trained model"""
        if self.model is None:
            raise ValueError("Model not trained yet")
        
        predictions = self.model.predict(X, verbose=0)
        
        # Inverse transform predictions
        if self.config.prediction_horizon == 1:
            predictions = predictions.flatten()
        
        predictions_rescaled = self.scaler.inverse_transform(
            predictions.reshape(-1, 1)
        ).flatten()
        
        return predictions_rescaled
    
    def evaluate(self, X_test: np.ndarray, y_test: np.ndarray) -> Dict:
        """Evaluate model performance"""
        logger.info("Evaluating model performance...")
        
        # Make predictions
        predictions = self.predict(X_test)
        
        # Inverse transform actual values
        y_test_rescaled = self.scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()
        
        # Calculate metrics
        mse = mean_squared_error(y_test_rescaled, predictions)
        mae = mean_absolute_error(y_test_rescaled, predictions)
        rmse = np.sqrt(mse)
        
        # Calculate percentage errors
        mape = np.mean(np.abs((y_test_rescaled - predictions) / y_test_rescaled)) * 100
        
        # Directional accuracy (for trading signals)
        actual_direction = np.diff(y_test_rescaled) > 0
        pred_direction = np.diff(predictions) > 0
        directional_accuracy = np.mean(actual_direction == pred_direction) * 100
        
        metrics = {
            'mse': mse,
            'mae': mae,
            'rmse': rmse,
            'mape': mape,
            'directional_accuracy': directional_accuracy,
            'predictions': predictions.tolist(),
            'actual': y_test_rescaled.tolist()
        }
        
        logger.info(f"Model Performance:")
        logger.info(f"  RMSE: ${rmse:.2f}")
        logger.info(f"  MAE: ${mae:.2f}")
        logger.info(f"  MAPE: {mape:.2f}%")
        logger.info(f"  Directional Accuracy: {directional_accuracy:.2f}%")
        
        return metrics
    
    def generate_trading_signals(self, data: pd.DataFrame, threshold: float = 0.02) -> pd.DataFrame:
        """
        Generate trading signals based on LSTM predictions
        
        Args:
            data: Input data for prediction
            threshold: Minimum price change percentage to generate signal
        
        Returns:
            DataFrame with predictions and trading signals
        """
        logger.info("Generating trading signals from LSTM predictions...")
        
        # Prepare data for prediction
        feature_data = data[self.feature_columns].copy()
        feature_data = feature_data.fillna(method='ffill').fillna(method='bfill')
        scaled_features = self.feature_scaler.transform(feature_data)
        
        # Create sequences for prediction
        X_pred = []
        valid_indices = []
        
        for i in range(self.config.sequence_length, len(scaled_features)):
            X_pred.append(scaled_features[i - self.config.sequence_length:i])
            valid_indices.append(i)
        
        if not X_pred:
            raise ValueError("Not enough data for prediction")
        
        X_pred = np.array(X_pred)
        
        # Make predictions
        predictions = self.predict(X_pred)
        
        # Create results DataFrame
        results = data.iloc[valid_indices].copy()
        results['predicted_price'] = predictions
        results['current_price'] = results[self.config.target]
        results['price_change_pct'] = (results['predicted_price'] - results['current_price']) / results['current_price'] * 100
        
        # Generate signals
        results['signal'] = 0  # Hold
        results.loc[results['price_change_pct'] > threshold, 'signal'] = 1  # Buy
        results.loc[results['price_change_pct'] < -threshold, 'signal'] = -1  # Sell
        
        # Add signal names
        signal_map = {-1: 'SELL', 0: 'HOLD', 1: 'BUY'}
        results['signal_name'] = results['signal'].map(signal_map)
        
        # Calculate confidence (based on magnitude of predicted change)
        results['confidence'] = np.abs(results['price_change_pct']) / 10.0  # Scale to 0-1
        results['confidence'] = np.clip(results['confidence'], 0, 1)
        
        signal_counts = results['signal_name'].value_counts()
        logger.info(f"Generated signals: {dict(signal_counts)}")
        
        return results[['predicted_price', 'current_price', 'price_change_pct', 
                       'signal', 'signal_name', 'confidence']]
    
    def save_model(self, filepath: str) -> None:
        """Save trained model and scalers"""
        filepath = Path(filepath)
        filepath.parent.mkdir(parents=True, exist_ok=True)
        
        # Save model
        self.model.save(f"{filepath}_model.h5")
        
        # Save scalers
        joblib.dump(self.scaler, f"{filepath}_target_scaler.pkl")
        joblib.dump(self.feature_scaler, f"{filepath}_feature_scaler.pkl")
        
        # Save config and metadata
        metadata = {
            'config': self.config.__dict__,
            'feature_columns': self.feature_columns,
            'training_history': self.training_history
        }
        
        with open(f"{filepath}_metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2, default=str)
        
        logger.info(f"Model saved to {filepath}")
    
    def load_model(self, filepath: str) -> None:
        """Load trained model and scalers"""
        filepath = Path(filepath)
        
        # Load model
        self.model = keras.models.load_model(f"{filepath}_model.h5")
        
        # Load scalers
        self.scaler = joblib.load(f"{filepath}_target_scaler.pkl")
        self.feature_scaler = joblib.load(f"{filepath}_feature_scaler.pkl")
        
        # Load metadata
        with open(f"{filepath}_metadata.json", 'r') as f:
            metadata = json.load(f)
        
        self.feature_columns = metadata['feature_columns']
        self.training_history = metadata.get('training_history')
        
        logger.info(f"Model loaded from {filepath}")
