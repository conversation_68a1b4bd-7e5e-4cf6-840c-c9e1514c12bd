#!/usr/bin/env python3
"""
Debug version of the Interactive Trading Simulator
Enhanced logging and error handling for troubleshooting
"""

import sys
import os
import logging
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
sys.path.append(os.path.dirname(__file__))

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('debug_simulation.log')
    ]
)

logger = logging.getLogger(__name__)

def test_portfolio_initialization():
    """Test portfolio initialization in isolation."""
    logger.info("=== Testing Portfolio Initialization ===")
    
    try:
        from src.simulation.portfolio_tracker import PortfolioTracker
        
        # Test with different capital amounts
        test_capitals = [10000.0, 100000.0, 50000.0]
        
        for capital in test_capitals:
            logger.info(f"Testing portfolio with ${capital:,.2f}")
            portfolio = PortfolioTracker(capital)
            
            logger.info(f"Portfolio created:")
            logger.info(f"  Initial Capital: ${portfolio.initial_capital:,.2f}")
            logger.info(f"  Current Cash: ${portfolio.cash:,.2f}")
            logger.info(f"  Positions: {len(portfolio.positions)}")
            
            # Test portfolio value calculation
            portfolio_value = portfolio.get_portfolio_value({})
            logger.info(f"  Portfolio Value: ${portfolio_value:,.2f}")
            
            assert portfolio.cash == capital, f"Cash mismatch: {portfolio.cash} != {capital}"
            assert portfolio_value == capital, f"Value mismatch: {portfolio_value} != {capital}"
            
            logger.info(f"✅ Portfolio test passed for ${capital:,.2f}")
            
    except Exception as e:
        logger.error(f"❌ Portfolio initialization failed: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    
    return True

def test_simulation_engine():
    """Test simulation engine initialization."""
    logger.info("=== Testing Simulation Engine ===")
    
    try:
        from src.simulation.simulation_engine import SimulationEngine, SimulationConfig
        
        # Create configuration
        config = SimulationConfig(
            initial_capital=100000.0,
            position_size=0.15,
            commission_rate=0.001,
            slippage_rate=0.0005
        )
        
        logger.info(f"Config created: ${config.initial_capital:,.2f}")
        
        # Create simulation engine
        engine = SimulationEngine(config)
        
        logger.info(f"Engine created:")
        logger.info(f"  Portfolio Cash: ${engine.portfolio.cash:,.2f}")
        logger.info(f"  Portfolio Initial: ${engine.portfolio.initial_capital:,.2f}")
        
        assert engine.portfolio.cash == config.initial_capital, f"Engine cash mismatch: {engine.portfolio.cash} != {config.initial_capital}"
        
        logger.info("✅ Simulation engine test passed")
        
    except Exception as e:
        logger.error(f"❌ Simulation engine failed: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    
    return True

def test_data_loading():
    """Test data loading functionality."""
    logger.info("=== Testing Data Loading ===")
    
    try:
        from src.data.openbb_client import OpenBBDataClient
        from datetime import datetime, timedelta

        client = OpenBBDataClient()
        logger.info("OpenBBDataClient created")

        # Load BTC data
        data = client.get_crypto_data(
            symbol="BTC-USD",
            provider="yfinance",
            start_date=datetime.now() - timedelta(days=30),
            end_date=datetime.now()
        )
        
        if data is not None and not data.empty:
            logger.info(f"✅ Data loaded: {len(data)} rows")
            logger.info(f"  Date range: {data.index[0]} to {data.index[-1]}")
            logger.info(f"  Columns: {list(data.columns)}")
            logger.info(f"  Price range: ${data['close'].min():.2f} - ${data['close'].max():.2f}")
            return True
        else:
            logger.error("❌ No data loaded")
            return False
            
    except Exception as e:
        logger.error(f"❌ Data loading failed: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_momentum_strategy():
    """Test momentum strategy in isolation."""
    logger.info("=== Testing Momentum Strategy ===")
    
    try:
        from src.strategies.ensemble_strategy import SimpleMomentumStrategy
        from src.data.openbb_client import OpenBBDataClient
        from datetime import datetime, timedelta

        # Create strategy
        strategy = SimpleMomentumStrategy({
            'name': 'Test Momentum',
            'description': 'Test momentum strategy',
            'lookback_period': 10,
            'momentum_threshold': 0.005
        })

        logger.info("Momentum strategy created")

        # Load test data
        client = OpenBBDataClient()
        data = client.get_crypto_data(
            symbol="BTC-USD",
            provider="yfinance",
            start_date=datetime.now() - timedelta(days=30),
            end_date=datetime.now()
        )
        
        if data is not None and not data.empty:
            # Test signal generation
            signals = []
            for i in range(15, min(25, len(data))):  # Test 10 signals
                current_data = data.iloc[:i+1]
                signal_list = strategy.generate_signals(current_data)
                if signal_list:
                    signal = signal_list[-1]  # Get the latest signal
                    signals.append(signal)
                    logger.info(f"  Signal {i}: {signal.signal.name} (confidence: {signal.confidence:.3f})")
            
            logger.info(f"✅ Generated {len(signals)} signals successfully")
            return True
        else:
            logger.error("❌ No data for strategy testing")
            return False
            
    except Exception as e:
        logger.error(f"❌ Momentum strategy failed: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def run_simple_simulation():
    """Run a simple simulation with debug output."""
    logger.info("=== Running Simple Simulation ===")
    
    try:
        from src.simulation.simulation_engine import SimulationEngine, SimulationConfig
        from src.strategies.ensemble_strategy import SimpleMomentumStrategy
        from src.data.openbb_client import OpenBBDataClient
        from datetime import datetime, timedelta
        
        # Create configuration
        config = SimulationConfig(
            initial_capital=100000.0,
            position_size=0.15,
            commission_rate=0.001,
            slippage_rate=0.0005
        )
        
        # Create engine
        engine = SimulationEngine(config)
        logger.info(f"Engine initialized with ${engine.portfolio.cash:,.2f}")
        
        # Create strategy
        strategy = SimpleMomentumStrategy({
            'name': 'Debug Momentum',
            'description': 'Debug momentum strategy',
            'lookback_period': 10,
            'momentum_threshold': 0.005
        })
        
        # Load data
        client = OpenBBDataClient()
        data = client.get_crypto_data(
            symbol="BTC-USD",
            provider="yfinance",
            start_date=datetime.now() - timedelta(days=30),
            end_date=datetime.now()
        )
        
        if data is None or data.empty:
            logger.error("❌ No data available")
            return False
        
        logger.info(f"Data loaded: {len(data)} rows")
        
        # Set up simulation
        engine.load_data(data)
        engine.set_strategy(strategy)
        
        logger.info(f"Before simulation - Cash: ${engine.portfolio.cash:,.2f}")
        
        # Run just a few steps manually
        for i in range(min(5, len(data) - 15)):
            step_index = 15 + i  # Start after lookback period
            current_data = data.iloc[:step_index+1]
            current_price = data.iloc[step_index]['close']
            
            logger.info(f"\n--- Step {i+1} (index {step_index}) ---")
            logger.info(f"Price: ${current_price:.2f}")
            logger.info(f"Cash before: ${engine.portfolio.cash:.2f}")
            
            # Generate signal
            signal_list = strategy.generate_signals(current_data)
            if signal_list:
                signal = signal_list[-1]  # Get the latest signal
                logger.info(f"Signal: {signal.signal.name} (confidence: {signal.confidence:.3f})")
            else:
                logger.info("No signals generated")
                continue
            
            # Process signal manually
            if signal.signal.name != 'HOLD':
                portfolio_value = engine.portfolio.get_portfolio_value({'BTC-USD': current_price})
                logger.info(f"Portfolio value: ${portfolio_value:.2f}")
                
                if signal.signal.name == 'BUY' and engine.portfolio.cash > 1000:
                    # Calculate buy quantity
                    position_size_dollars = portfolio_value * config.position_size
                    quantity = position_size_dollars / current_price
                    cost = quantity * current_price * (1 + config.commission_rate)
                    
                    logger.info(f"Buy calculation: quantity={quantity:.6f}, cost=${cost:.2f}")
                    
                    if cost <= engine.portfolio.cash:
                        logger.info(f"Executing BUY order")
                        # This would execute the trade
                    else:
                        logger.info(f"Insufficient cash for BUY: need ${cost:.2f}, have ${engine.portfolio.cash:.2f}")
            
            logger.info(f"Cash after: ${engine.portfolio.cash:.2f}")
        
        logger.info("✅ Simple simulation completed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Simple simulation failed: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """Run comprehensive debug tests."""
    logger.info("🔧 Starting Debug Session")
    logger.info("=" * 50)
    
    tests = [
        ("Portfolio Initialization", test_portfolio_initialization),
        ("Simulation Engine", test_simulation_engine),
        ("Data Loading", test_data_loading),
        ("Momentum Strategy", test_momentum_strategy),
        ("Simple Simulation", run_simple_simulation),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running: {test_name}")
        logger.info("-" * 30)
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.info(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            logger.error(f"💥 {test_name}: CRASHED - {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("🎯 DEBUG SUMMARY")
    logger.info("=" * 50)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status}: {test_name}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! System is working correctly.")
        return True
    else:
        logger.info("🔧 Some tests failed. Check logs above for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
