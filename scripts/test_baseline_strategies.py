#!/usr/bin/env python3
"""Test script for baseline trading strategies with OpenBB data."""

import sys
import os
import logging
from datetime import datetime, timedelta
import pandas as pd
import matplotlib.pyplot as plt

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from data.openbb_client import OpenBBDataClient
from data.data_validator import DataValidator
from strategies.ma_crossover import MovingAverageCrossoverStrategy
from strategies.rsi_mean_reversion import RSIMeanReversionStrategy
from backtesting.backtest_engine import BacktestEngine, BacktestConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_data_providers():
    """Test OpenBB data providers and validation."""
    logger.info("🔍 Testing OpenBB Data Providers...")

    # Initialize client
    client = OpenBBDataClient()
    validator = DataValidator(client.config)

    # Test single provider
    try:
        data = client.get_crypto_data(
            symbol="BTC-USD",
            provider="yfinance",
            start_date=datetime.now() - timedelta(days=365),
            end_date=datetime.now()
        )

        logger.info(f"✅ YFinance data: {len(data)} records")
        logger.info(f"   Date range: {data.index[0]} to {data.index[-1]}")
        logger.info(f"   Columns: {list(data.columns)}")

        # Validate data quality
        validation_result = validator.validate_single_provider(data, "yfinance")
        logger.info(f"   Quality score: {validation_result.quality_score:.2%}")

        if validation_result.issues:
            logger.warning(f"   Issues: {validation_result.issues}")

        return data

    except Exception as e:
        logger.error(f"❌ Data provider test failed: {e}")
        return None


def test_ma_crossover_strategy(data: pd.DataFrame):
    """Test Moving Average Crossover strategy."""
    logger.info("📈 Testing MA Crossover Strategy...")

    # Configure strategy
    config = {
        'fast_period': 20,
        'slow_period': 50,
        'ma_type': 'sma',
        'min_crossover_strength': 0.01,
        'max_position_size': 1.0,
        'stop_loss_percent': 0.05,
        'take_profit_percent': 0.10
    }

    strategy = MovingAverageCrossoverStrategy(config)

    # Calculate indicators
    data_with_indicators = strategy.calculate_indicators(data)
    logger.info(f"   Added indicators: {[col for col in data_with_indicators.columns if col not in data.columns]}")

    # Generate recent signals
    recent_data = data_with_indicators.tail(100)  # Last 100 days
    signals = strategy.generate_signals(recent_data)

    signal_counts = {}
    for signal in signals:
        signal_counts[signal.signal.value] = signal_counts.get(signal.signal.value, 0) + 1

    logger.info(f"   Recent signals (last 100 days): {signal_counts}")

    # Show latest signal
    if signals:
        latest_signal = signals[-1]
        logger.info(f"   Latest signal: {latest_signal.signal.value} "
                   f"(confidence: {latest_signal.confidence:.2%}, "
                   f"reason: {latest_signal.reason})")

    return strategy, data_with_indicators


def test_rsi_strategy(data: pd.DataFrame):
    """Test RSI Mean Reversion strategy."""
    logger.info("📊 Testing RSI Mean Reversion Strategy...")

    # Configure strategy
    config = {
        'rsi_period': 14,
        'oversold_threshold': 30,
        'overbought_threshold': 70,
        'extreme_oversold': 20,
        'extreme_overbought': 80,
        'max_position_size': 1.0,
        'stop_loss_percent': 0.05,
        'take_profit_percent': 0.10
    }

    strategy = RSIMeanReversionStrategy(config)

    # Calculate indicators
    data_with_indicators = strategy.calculate_indicators(data)
    logger.info(f"   Added indicators: {[col for col in data_with_indicators.columns if col not in data.columns]}")

    # Generate recent signals
    recent_data = data_with_indicators.tail(100)  # Last 100 days
    signals = strategy.generate_signals(recent_data)

    signal_counts = {}
    for signal in signals:
        signal_counts[signal.signal.value] = signal_counts.get(signal.signal.value, 0) + 1

    logger.info(f"   Recent signals (last 100 days): {signal_counts}")

    # Show latest signal
    if signals:
        latest_signal = signals[-1]
        logger.info(f"   Latest signal: {latest_signal.signal.value} "
                   f"(confidence: {latest_signal.confidence:.2%}, "
                   f"reason: {latest_signal.reason})")

    # Show current RSI level
    current_rsi = data_with_indicators['rsi'].iloc[-1]
    logger.info(f"   Current RSI: {current_rsi:.1f}")

    return strategy, data_with_indicators


def run_backtest(strategy, data: pd.DataFrame, strategy_name: str):
    """Run backtest for a strategy."""
    logger.info(f"🔄 Running backtest for {strategy_name}...")

    # Configure backtest
    config = BacktestConfig(
        initial_capital=10000.0,
        commission_rate=0.001,  # 0.1%
        slippage_rate=0.0005,   # 0.05%
        max_positions=1,
        position_sizing="fixed",
        position_size=0.95      # 95% of capital
    )

    engine = BacktestEngine(config)

    # Run backtest on last 6 months
    end_date = data.index[-1]
    start_date = end_date - timedelta(days=180)

    try:
        results = engine.run_backtest(
            strategy=strategy,
            data=data,
            start_date=start_date,
            end_date=end_date
        )

        # Log results
        logger.info(f"   📊 {strategy_name} Results:")
        logger.info(f"      Total Return: {results.total_return:.2%}")
        logger.info(f"      Annualized Return: {results.annualized_return:.2%}")
        logger.info(f"      Sharpe Ratio: {results.sharpe_ratio:.2f}")
        logger.info(f"      Max Drawdown: {results.max_drawdown:.2%}")
        logger.info(f"      Total Trades: {results.total_trades}")
        logger.info(f"      Win Rate: {results.win_rate:.2%}")

        if results.total_trades > 0:
            logger.info(f"      Avg Win: ${results.avg_win:.2f}")
            logger.info(f"      Avg Loss: ${results.avg_loss:.2f}")
            logger.info(f"      Profit Factor: {results.profit_factor:.2f}")

        return results

    except Exception as e:
        logger.error(f"   ❌ Backtest failed: {e}")
        return None


def create_comparison_chart(results_dict: dict, data: pd.DataFrame):
    """Create comparison chart of strategies."""
    logger.info("📈 Creating strategy comparison chart...")

    try:
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

        # Plot 1: Equity curves
        ax1.plot(data.index, data['close'], label='BTC Price', alpha=0.7)
        ax1_twin = ax1.twinx()

        for name, results in results_dict.items():
            if results:
                ax1_twin.plot(results.equity_curve.index, results.equity_curve,
                            label=f'{name} Equity', linewidth=2)

        ax1.set_title('Price vs Strategy Performance')
        ax1.set_ylabel('BTC Price ($)')
        ax1_twin.set_ylabel('Portfolio Value ($)')
        ax1.legend(loc='upper left')
        ax1_twin.legend(loc='upper right')

        # Plot 2: Returns comparison
        returns_data = []
        names = []
        for name, results in results_dict.items():
            if results:
                returns_data.append(results.total_return * 100)
                names.append(name)

        if returns_data:
            ax2.bar(names, returns_data)
            ax2.set_title('Total Returns Comparison')
            ax2.set_ylabel('Return (%)')
            ax2.tick_params(axis='x', rotation=45)

        # Plot 3: Risk metrics
        sharpe_data = []
        dd_data = []
        for name, results in results_dict.items():
            if results:
                sharpe_data.append(results.sharpe_ratio)
                dd_data.append(abs(results.max_drawdown) * 100)

        if sharpe_data:
            x = range(len(names))
            ax3.bar([i - 0.2 for i in x], sharpe_data, 0.4, label='Sharpe Ratio')
            ax3_twin = ax3.twinx()
            ax3_twin.bar([i + 0.2 for i in x], dd_data, 0.4, label='Max DD (%)', color='red', alpha=0.7)

            ax3.set_title('Risk-Adjusted Performance')
            ax3.set_ylabel('Sharpe Ratio')
            ax3_twin.set_ylabel('Max Drawdown (%)')
            ax3.set_xticks(x)
            ax3.set_xticklabels(names)
            ax3.legend(loc='upper left')
            ax3_twin.legend(loc='upper right')

        # Plot 4: Trade statistics
        trade_counts = []
        win_rates = []
        for name, results in results_dict.items():
            if results:
                trade_counts.append(results.total_trades)
                win_rates.append(results.win_rate * 100)

        if trade_counts:
            ax4.bar([i - 0.2 for i in x], trade_counts, 0.4, label='Total Trades')
            ax4_twin = ax4.twinx()
            ax4_twin.bar([i + 0.2 for i in x], win_rates, 0.4, label='Win Rate (%)', color='green', alpha=0.7)

            ax4.set_title('Trading Statistics')
            ax4.set_ylabel('Number of Trades')
            ax4_twin.set_ylabel('Win Rate (%)')
            ax4.set_xticks(x)
            ax4.set_xticklabels(names)
            ax4.legend(loc='upper left')
            ax4_twin.legend(loc='upper right')

        plt.tight_layout()

        # Save chart
        chart_path = 'baseline_strategies_comparison.png'
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        logger.info(f"   💾 Chart saved: {chart_path}")

        plt.show()

    except Exception as e:
        logger.error(f"   ❌ Chart creation failed: {e}")


def main():
    """Main test function."""
    logger.info("🚀 Starting Baseline Strategy Testing...")

    # Test data providers
    data = test_data_providers()
    if data is None:
        logger.error("❌ Cannot proceed without data")
        return

    # Test strategies
    ma_strategy, ma_data = test_ma_crossover_strategy(data)
    rsi_strategy, rsi_data = test_rsi_strategy(data)

    # Run backtests
    results = {}
    results['MA Crossover'] = run_backtest(ma_strategy, ma_data, 'MA Crossover')
    results['RSI Mean Reversion'] = run_backtest(rsi_strategy, rsi_data, 'RSI Mean Reversion')

    # Create comparison
    create_comparison_chart(results, data)

    # Summary
    logger.info("📋 SUMMARY:")
    logger.info("✅ Codebase cleaned and organized")
    logger.info("✅ OpenBB data foundation implemented")
    logger.info("✅ Data validation system created")
    logger.info("✅ Baseline strategies implemented")
    logger.info("✅ Comprehensive backtesting engine built")
    logger.info("")
    logger.info("🎯 Next Steps:")
    logger.info("   1. Fine-tune strategy parameters")
    logger.info("   2. Add more data providers (Tiingo, Alpha Vantage)")
    logger.info("   3. Implement ensemble strategies")
    logger.info("   4. Add real-time trading capabilities")
    logger.info("   5. Create web dashboard for monitoring")


if __name__ == "__main__":
    main()
