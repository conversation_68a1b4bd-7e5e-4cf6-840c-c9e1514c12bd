#!/usr/bin/env python3
"""Debug script for portfolio initialization."""

import sys
import os
import logging

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

from src.simulation.portfolio_tracker import PortfolioTracker
from src.simulation.simulation_engine import SimulationEngine, SimulationConfig

def test_portfolio_direct():
    """Test portfolio tracker directly."""
    print("🧪 Testing Portfolio Tracker Directly")
    print("=" * 50)
    
    # Test direct initialization
    initial_capital = 100000.0
    portfolio = PortfolioTracker(initial_capital)
    
    print(f"Initial capital: ${initial_capital:,.2f}")
    print(f"Portfolio cash: ${portfolio.cash:,.2f}")
    print(f"Portfolio initial_capital: ${portfolio.initial_capital:,.2f}")
    
    # Test portfolio value calculation
    prices = {'BTC-USD': 50000.0}
    portfolio_value = portfolio.get_portfolio_value(prices)
    print(f"Portfolio value: ${portfolio_value:,.2f}")
    
    return portfolio

def test_simulation_config():
    """Test simulation configuration."""
    print("\n🧪 Testing Simulation Configuration")
    print("=" * 50)
    
    config = SimulationConfig()
    print(f"Default initial capital: ${config.initial_capital:,.2f}")
    
    # Test with custom capital
    config.initial_capital = 100000.0
    print(f"Updated initial capital: ${config.initial_capital:,.2f}")
    
    return config

def test_simulation_engine():
    """Test simulation engine initialization."""
    print("\n🧪 Testing Simulation Engine")
    print("=" * 50)
    
    config = SimulationConfig()
    config.initial_capital = 100000.0
    
    print(f"Config initial capital: ${config.initial_capital:,.2f}")
    
    engine = SimulationEngine(config)
    
    print(f"Engine config initial capital: ${engine.config.initial_capital:,.2f}")
    print(f"Engine portfolio cash: ${engine.portfolio.cash:,.2f}")
    print(f"Engine portfolio initial_capital: ${engine.portfolio.initial_capital:,.2f}")
    
    return engine

def main():
    """Run debug tests."""
    print("🚀 Portfolio Debug Session")
    print("=" * 60)
    
    try:
        # Test 1: Direct portfolio
        portfolio = test_portfolio_direct()
        
        # Test 2: Configuration
        config = test_simulation_config()
        
        # Test 3: Simulation engine
        engine = test_simulation_engine()
        
        print("\n🎉 Debug session complete!")
        print("📊 Summary:")
        print(f"  Direct portfolio cash: ${portfolio.cash:,.2f}")
        print(f"  Engine portfolio cash: ${engine.portfolio.cash:,.2f}")
        
        if portfolio.cash == 100000.0 and engine.portfolio.cash == 100000.0:
            print("✅ Portfolio initialization working correctly")
        else:
            print("❌ Portfolio initialization issue detected")
        
    except Exception as e:
        print(f"❌ Error during debug: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
