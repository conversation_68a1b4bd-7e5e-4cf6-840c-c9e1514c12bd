#!/usr/bin/env python3
"""Debug script for ensemble strategy."""

import sys
import os
import logging
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

# Configure logging to see debug output
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

from src.strategies.ensemble_strategy import EnsembleStrategy, SimpleMomentumStrategy
from src.strategies.ma_crossover import MovingAverageCrossoverStrategy
from src.strategies.rsi_mean_reversion import RSIMeanReversionStrategy

def create_test_data():
    """Create test BTC data."""
    dates = pd.date_range(start='2024-01-01', end='2024-06-01', freq='D')
    
    # Create realistic BTC price data with more volatility
    np.random.seed(42)
    base_price = 50000
    prices = []
    current_price = base_price
    
    for i in range(len(dates)):
        # Add some trend and volatility
        trend = 0.002 * (1 + 0.5 * np.sin(i / 30))  # Cyclical trend
        volatility = np.random.normal(0, 0.03)  # 3% daily volatility
        
        current_price *= (1 + trend + volatility)
        prices.append(current_price)
    
    data = pd.DataFrame({
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'close': prices,
        'volume': [np.random.randint(1000, 10000) for _ in prices]
    }, index=dates)
    
    return data

def test_individual_strategies():
    """Test individual strategies first."""
    print("🧪 Testing Individual Strategies")
    print("=" * 50)
    
    data = create_test_data()
    print(f"📊 Data shape: {data.shape}")
    print(f"📊 Price range: ${data['close'].min():.2f} - ${data['close'].max():.2f}")
    print()
    
    # Test Momentum Strategy
    print("1. Testing Momentum Strategy:")
    momentum_strategy = SimpleMomentumStrategy({
        'lookback_period': 10,
        'momentum_threshold': 0.005
    })
    
    momentum_signals = momentum_strategy.generate_signals(data)
    print(f"   Generated {len(momentum_signals)} signals")
    for signal in momentum_signals[:3]:  # Show first 3
        print(f"   - {signal.signal} at {signal.timestamp.date()} confidence {signal.confidence:.3f}")
    print()
    
    # Test MA Crossover Strategy
    print("2. Testing MA Crossover Strategy:")
    ma_strategy = MovingAverageCrossoverStrategy({
        'fast_period': 10,
        'slow_period': 30,
        'ma_type': 'sma',
        'min_crossover_strength': 0.0,
        'stop_loss_percent': 0.03,
        'take_profit_percent': 0.08
    })
    
    ma_signals = ma_strategy.generate_signals(data)
    print(f"   Generated {len(ma_signals)} signals")
    for signal in ma_signals[:3]:  # Show first 3
        print(f"   - {signal.signal} at {signal.timestamp.date()} confidence {signal.confidence:.3f}")
    print()
    
    # Test RSI Strategy
    print("3. Testing RSI Strategy:")
    rsi_strategy = RSIMeanReversionStrategy({
        'rsi_period': 14,
        'oversold_threshold': 30,
        'overbought_threshold': 70,
        'stop_loss_percent': 0.03,
        'take_profit_percent': 0.08
    })
    
    rsi_signals = rsi_strategy.generate_signals(data)
    print(f"   Generated {len(rsi_signals)} signals")
    for signal in rsi_signals[:3]:  # Show first 3
        print(f"   - {signal.signal} at {signal.timestamp.date()} confidence {signal.confidence:.3f}")
    print()
    
    return data, momentum_signals, ma_signals, rsi_signals

def test_ensemble_strategy():
    """Test ensemble strategy with debugging."""
    print("🧪 Testing Ensemble Strategy")
    print("=" * 50)
    
    data, momentum_signals, ma_signals, rsi_signals = test_individual_strategies()
    
    # Create ensemble with relaxed parameters
    ensemble_params = {
        "ensemble_method": "confidence_weighted",
        "min_consensus": 0.3,  # Reduced from 0.6
        "confidence_threshold": 0.3,  # Reduced from 0.5
        "strategy_weights": {
            "ma_crossover": 0.33,
            "rsi_mean_reversion": 0.33,
            "momentum": 0.34
        },
        "ma_crossover_params": {
            "fast_period": 10,
            "slow_period": 30,
            "ma_type": "sma",
            "min_crossover_strength": 0.0,
            "stop_loss_percent": 0.03,
            "take_profit_percent": 0.08
        },
        "rsi_params": {
            "rsi_period": 14,
            "oversold_threshold": 30,
            "overbought_threshold": 70,
            "stop_loss_percent": 0.03,
            "take_profit_percent": 0.08
        },
        "momentum_params": {
            "lookback_period": 10,
            "momentum_threshold": 0.005
        }
    }
    
    print("Ensemble Configuration:")
    print(f"  Min consensus: {ensemble_params['min_consensus']}")
    print(f"  Confidence threshold: {ensemble_params['confidence_threshold']}")
    print(f"  Method: {ensemble_params['ensemble_method']}")
    print()
    
    ensemble_strategy = EnsembleStrategy(ensemble_params)
    
    print("Testing ensemble signal generation...")
    ensemble_signals = ensemble_strategy.generate_signals(data)
    
    print(f"🎯 Ensemble generated {len(ensemble_signals)} signals")
    
    for signal in ensemble_signals:
        print(f"   - {signal.signal} at {signal.timestamp.date()} confidence {signal.confidence:.3f}")
        print(f"     Reason: {signal.reason}")
    
    return ensemble_signals

def main():
    """Run debug tests."""
    print("🚀 Ensemble Strategy Debug Session")
    print("=" * 60)
    print()
    
    try:
        ensemble_signals = test_ensemble_strategy()
        
        print()
        print("🎉 Debug session complete!")
        print(f"📊 Summary: Ensemble generated {len(ensemble_signals)} signals")
        
        if len(ensemble_signals) == 0:
            print("❌ Issue: No signals generated - consensus or confidence thresholds too high")
            print("💡 Suggestion: Reduce min_consensus and confidence_threshold parameters")
        else:
            print("✅ Success: Ensemble is generating signals")
        
    except Exception as e:
        print(f"❌ Error during debug: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
