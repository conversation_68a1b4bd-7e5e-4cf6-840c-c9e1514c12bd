#!/usr/bin/env python3
"""Test script to verify simulator fixes."""

import sys
import os
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.strategies.ensemble_strategy import SimpleMomentumStrategy
from src.simulation.simulation_engine import SimulationEngine, SimulationConfig
from src.simulation.portfolio_tracker import PortfolioTracker

def create_test_data():
    """Create test BTC data."""
    dates = pd.date_range(start='2024-01-01', end='2024-06-01', freq='D')
    
    # Create realistic BTC price data
    np.random.seed(42)
    base_price = 50000
    prices = []
    current_price = base_price
    
    for i in range(len(dates)):
        # Add some trend and volatility
        trend = 0.001 * (1 + 0.5 * np.sin(i / 30))  # Cyclical trend
        volatility = np.random.normal(0, 0.02)  # 2% daily volatility
        
        current_price *= (1 + trend + volatility)
        prices.append(current_price)
    
    data = pd.DataFrame({
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'close': prices,
        'volume': [np.random.randint(1000, 10000) for _ in prices]
    }, index=dates)
    
    return data

def test_portfolio_tracker():
    """Test portfolio tracker functionality."""
    print("🧪 Testing Portfolio Tracker...")
    
    portfolio = PortfolioTracker(initial_capital=10000.0)
    
    # Check initial state
    assert portfolio.cash == 10000.0, f"Expected cash 10000, got {portfolio.cash}"
    assert portfolio.get_portfolio_value() == 10000.0, f"Expected value 10000, got {portfolio.get_portfolio_value()}"
    
    print("✅ Portfolio tracker initialization works")
    
    # Test trade execution
    trade = portfolio.execute_trade(
        symbol='BTC-USD',
        side='BUY',
        quantity=0.1,
        price=50000.0,
        strategy='TestStrategy',
        reason='Test trade',
        confidence=0.8
    )
    
    expected_cost = 0.1 * 50000.0 * (1 + 0.001 + 0.0005)  # Including commission and slippage
    expected_cash = 10000.0 - expected_cost
    
    print(f"💰 After BUY trade: Cash = ${portfolio.cash:.2f}, Expected = ${expected_cash:.2f}")
    assert abs(portfolio.cash - expected_cash) < 1.0, f"Cash mismatch: {portfolio.cash} vs {expected_cash}"
    
    print("✅ Trade execution works")
    return True

def test_momentum_strategy():
    """Test momentum strategy."""
    print("🧪 Testing Momentum Strategy...")

    data = create_test_data()
    strategy = SimpleMomentumStrategy({'lookback_period': 10, 'momentum_threshold': 0.005})

    # Test indicator calculation
    data_with_indicators = strategy.calculate_indicators(data)
    print(f"📊 Data shape: {data_with_indicators.shape}")
    print(f"📊 Columns: {list(data_with_indicators.columns)}")

    # Test signal generation
    signals = strategy.generate_signals(data_with_indicators)
    print(f"📈 Generated {len(signals)} signals")

    # Debug momentum values
    latest_momentum = data_with_indicators['momentum'].iloc[-1]
    latest_strength = data_with_indicators['momentum_strength'].iloc[-1]
    print(f"🔍 Latest momentum: {latest_momentum:.4f}, strength: {latest_strength:.4f}, threshold: {strategy.momentum_threshold}")

    if signals:
        print(f"📈 First signal: {signals[0].signal} at {signals[0].timestamp} with confidence {signals[0].confidence}")

    print("✅ Momentum strategy works")
    return True

def test_ensemble_strategy():
    """Test ensemble strategy."""
    print("🧪 Testing Ensemble Strategy...")

    from src.strategies.ensemble_strategy import EnsembleStrategy

    data = create_test_data()

    # Create ensemble with our champion configuration
    ensemble_params = {
        "ensemble_method": "confidence_weighted",
        "min_consensus": 0.6,
        "confidence_threshold": 0.5,
        "strategy_weights": {
            "ma_crossover": 0.33,
            "rsi_mean_reversion": 0.33,
            "momentum": 0.34
        },
        "momentum_params": {
            "lookback_period": 10,
            "momentum_threshold": 0.005
        }
    }

    strategy = EnsembleStrategy(ensemble_params)

    # Test signal generation
    signals = strategy.generate_signals(data)
    print(f"📈 Ensemble generated {len(signals)} signals")

    if signals:
        print(f"📈 First signal: {signals[0].signal} at {signals[0].timestamp} with confidence {signals[0].confidence}")
        print(f"📝 Reason: {signals[0].reason}")

    print("✅ Ensemble strategy works")
    return True

def test_simulation_engine():
    """Test simulation engine."""
    print("🧪 Testing Simulation Engine...")
    
    # Create test configuration
    config = SimulationConfig(
        initial_capital=10000.0,
        position_size=0.2,
        speed_multiplier=1.0
    )
    
    # Create simulation engine
    engine = SimulationEngine(config)
    
    # Check portfolio initialization
    assert engine.portfolio.cash == 10000.0, f"Expected cash 10000, got {engine.portfolio.cash}"
    print(f"💰 Portfolio initialized with ${engine.portfolio.cash:.2f}")
    
    # Load test data
    data = create_test_data()
    engine.load_data(data)
    
    # Set strategy
    strategy = SimpleMomentumStrategy({'lookback_period': 10, 'momentum_threshold': 0.005})
    engine.set_strategy(strategy)
    
    print("✅ Simulation engine setup works")
    
    # Test a few simulation steps
    print("🔄 Testing simulation steps...")
    engine.is_running = True
    
    for i in range(5):
        if engine.step_forward():
            state = engine.get_current_state()
            print(f"Step {i+1}: Portfolio = ${state['portfolio_value']:.2f}, Cash = ${state['cash']:.2f}")
        else:
            break
    
    print("✅ Simulation steps work")
    return True

def main():
    """Run all tests."""
    print("🚀 Testing Simulator Fixes")
    print("=" * 50)
    
    try:
        # Test individual components
        test_portfolio_tracker()
        print()
        
        test_momentum_strategy()
        print()

        test_ensemble_strategy()
        print()

        test_simulation_engine()
        print()
        
        print("🎉 All tests passed! Simulator fixes are working.")
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    main()
