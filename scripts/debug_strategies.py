#!/usr/bin/env python3
"""Debug strategy signal generation."""

import sys
import os
import logging
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.data.openbb_client import OpenBBDataClient
from src.strategies.ma_crossover import MovingAverageCrossoverStrategy
from src.strategies.rsi_mean_reversion import RSIMeanReversionStrategy

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def debug_ma_strategy():
    """Debug MA crossover strategy signal generation."""
    logger.info("🔍 Debugging MA Crossover Strategy...")
    
    # Load data
    client = OpenBBDataClient()
    data = client.get_crypto_data(
        symbol="BTC-USD",
        provider="yfinance",
        start_date=datetime.now() - timedelta(days=90),
        end_date=datetime.now()
    )
    
    logger.info(f"Loaded {len(data)} records")
    
    # Create strategy
    params = {
        'fast_period': 10,
        'slow_period': 30,
        'ma_type': 'sma',
        'stop_loss_percent': 0.05,
        'take_profit_percent': 0.10
    }
    
    strategy = MovingAverageCrossoverStrategy(params)
    
    # Add indicators
    data_with_indicators = strategy.calculate_indicators(data)
    
    logger.info(f"Indicators added. Columns: {list(data_with_indicators.columns)}")
    
    # Check for crossovers in the data
    crossover_col = 'ma_crossover'
    if crossover_col in data_with_indicators.columns:
        crossovers = data_with_indicators[data_with_indicators[crossover_col].abs() == 2.0]
        logger.info(f"Found {len(crossovers)} crossover points")
        
        if len(crossovers) > 0:
            logger.info("Crossover details:")
            for idx, row in crossovers.head(5).iterrows():
                logger.info(f"  {idx}: crossover={row[crossover_col]}, strength={row.get('ma_strength', 'N/A')}")
    
    # Test signal generation at different points
    logger.info("\nTesting signal generation at various points:")
    
    signal_count = 0
    for i in range(max(50, strategy.slow_period + 10), len(data_with_indicators), 10):
        current_data = data_with_indicators.iloc[:i+1]
        signals = strategy.generate_signals(current_data)
        
        if signals:
            signal_count += len(signals)
            latest_signal = signals[-1]
            logger.info(f"  Point {i}: {len(signals)} signals, latest: {latest_signal.signal} at {latest_signal.timestamp}")
    
    logger.info(f"Total signals generated: {signal_count}")
    
    # Check the latest few rows for debugging
    logger.info("\nLatest data rows:")
    latest_data = data_with_indicators.tail(5)
    for idx, row in latest_data.iterrows():
        logger.info(f"  {idx}: close={row['close']:.2f}, crossover={row.get('ma_crossover', 'N/A')}, strength={row.get('ma_strength', 'N/A')}")


def debug_rsi_strategy():
    """Debug RSI strategy signal generation."""
    logger.info("\n🔍 Debugging RSI Strategy...")
    
    # Load data
    client = OpenBBDataClient()
    data = client.get_crypto_data(
        symbol="BTC-USD",
        provider="yfinance",
        start_date=datetime.now() - timedelta(days=90),
        end_date=datetime.now()
    )
    
    # Create strategy
    params = {
        'rsi_period': 14,
        'oversold_threshold': 30,
        'overbought_threshold': 70,
        'stop_loss_percent': 0.05,
        'take_profit_percent': 0.10
    }
    
    strategy = RSIMeanReversionStrategy(params)
    
    # Add indicators
    data_with_indicators = strategy.calculate_indicators(data)
    
    logger.info(f"Indicators added. Columns: {list(data_with_indicators.columns)}")
    
    # Check RSI levels
    if 'rsi' in data_with_indicators.columns:
        rsi_data = data_with_indicators['rsi'].dropna()
        oversold_points = rsi_data[rsi_data <= params['oversold_threshold']]
        overbought_points = rsi_data[rsi_data >= params['overbought_threshold']]
        
        logger.info(f"RSI oversold points (<= {params['oversold_threshold']}): {len(oversold_points)}")
        logger.info(f"RSI overbought points (>= {params['overbought_threshold']}): {len(overbought_points)}")
        
        if len(oversold_points) > 0:
            logger.info(f"Recent oversold levels: {oversold_points.tail(3).values}")
        if len(overbought_points) > 0:
            logger.info(f"Recent overbought levels: {overbought_points.tail(3).values}")
    
    # Test signal generation
    logger.info("\nTesting RSI signal generation:")
    
    signal_count = 0
    for i in range(max(30, params['rsi_period'] + 10), len(data_with_indicators), 10):
        current_data = data_with_indicators.iloc[:i+1]
        signals = strategy.generate_signals(current_data)
        
        if signals:
            signal_count += len(signals)
            latest_signal = signals[-1]
            logger.info(f"  Point {i}: {len(signals)} signals, latest: {latest_signal.signal} at {latest_signal.timestamp}")
    
    logger.info(f"Total RSI signals generated: {signal_count}")


def check_data_quality():
    """Check data quality and basic statistics."""
    logger.info("\n📊 Checking Data Quality...")
    
    client = OpenBBDataClient()
    data = client.get_crypto_data(
        symbol="BTC-USD",
        provider="yfinance",
        start_date=datetime.now() - timedelta(days=90),
        end_date=datetime.now()
    )
    
    logger.info(f"Data shape: {data.shape}")
    logger.info(f"Date range: {data.index[0]} to {data.index[-1]}")
    logger.info(f"Columns: {list(data.columns)}")
    
    # Check for missing values
    missing_values = data.isnull().sum()
    if missing_values.sum() > 0:
        logger.warning(f"Missing values found: {missing_values[missing_values > 0]}")
    else:
        logger.info("No missing values found")
    
    # Basic price statistics
    logger.info(f"Price range: ${data['close'].min():.2f} - ${data['close'].max():.2f}")
    logger.info(f"Latest price: ${data['close'].iloc[-1]:.2f}")
    
    # Calculate simple returns
    returns = data['close'].pct_change().dropna()
    logger.info(f"Daily return stats: mean={returns.mean():.4f}, std={returns.std():.4f}")
    
    # Check for sufficient volatility
    volatility = returns.std() * np.sqrt(252)  # Annualized
    logger.info(f"Annualized volatility: {volatility:.2%}")
    
    if volatility < 0.2:
        logger.warning("Low volatility detected - may result in fewer trading signals")


def main():
    """Main debugging function."""
    logger.info("🚀 Strategy Debugging Session")
    logger.info("=" * 60)
    
    # Check data quality first
    check_data_quality()
    
    # Debug MA strategy
    debug_ma_strategy()
    
    # Debug RSI strategy
    debug_rsi_strategy()
    
    logger.info("\n" + "=" * 60)
    logger.info("✅ Debugging complete")
    logger.info("\n🎯 Key Insights:")
    logger.info("   1. Check if crossovers are being detected")
    logger.info("   2. Verify signal generation logic")
    logger.info("   3. Ensure sufficient data for indicators")
    logger.info("   4. Review confidence thresholds")


if __name__ == "__main__":
    main()
