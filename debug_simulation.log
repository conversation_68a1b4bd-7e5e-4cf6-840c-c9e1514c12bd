2025-08-02 21:59:56,703 - __main__ - INFO - 🔧 Starting Debug Session
2025-08-02 21:59:56,703 - __main__ - INFO - ==================================================
2025-08-02 21:59:56,703 - __main__ - INFO - 
🧪 Running: Portfolio Initialization
2025-08-02 21:59:56,703 - __main__ - INFO - ------------------------------
2025-08-02 21:59:56,703 - __main__ - INFO - === Testing Portfolio Initialization ===
2025-08-02 21:59:57,277 - __main__ - ERROR - ❌ Portfolio initialization failed: attempted relative import beyond top-level package
2025-08-02 21:59:57,278 - __main__ - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Desktop/VS code file/test-1-open-bb-/debug_simulator.py", line 32, in test_portfolio_initialization
    from simulation.portfolio_tracker import PortfolioTracker
  File "/Users/<USER>/Desktop/VS code file/test-1-open-bb-/src/simulation/__init__.py", line 4, in <module>
    from .simulation_engine import SimulationEngine
  File "/Users/<USER>/Desktop/VS code file/test-1-open-bb-/src/simulation/simulation_engine.py", line 13, in <module>
    from ..strategies.base_strategy import BaseStrategy
ImportError: attempted relative import beyond top-level package

2025-08-02 21:59:57,278 - __main__ - INFO - ❌ Portfolio Initialization: FAILED
2025-08-02 21:59:57,278 - __main__ - INFO - 
🧪 Running: Simulation Engine
2025-08-02 21:59:57,278 - __main__ - INFO - ------------------------------
2025-08-02 21:59:57,278 - __main__ - INFO - === Testing Simulation Engine ===
2025-08-02 21:59:57,279 - __main__ - ERROR - ❌ Simulation engine failed: attempted relative import beyond top-level package
2025-08-02 21:59:57,279 - __main__ - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Desktop/VS code file/test-1-open-bb-/debug_simulator.py", line 68, in test_simulation_engine
    from simulation.simulation_engine import SimulationEngine, SimulationConfig
  File "/Users/<USER>/Desktop/VS code file/test-1-open-bb-/src/simulation/__init__.py", line 4, in <module>
    from .simulation_engine import SimulationEngine
  File "/Users/<USER>/Desktop/VS code file/test-1-open-bb-/src/simulation/simulation_engine.py", line 13, in <module>
    from ..strategies.base_strategy import BaseStrategy
ImportError: attempted relative import beyond top-level package

2025-08-02 21:59:57,279 - __main__ - INFO - ❌ Simulation Engine: FAILED
2025-08-02 21:59:57,279 - __main__ - INFO - 
🧪 Running: Data Loading
2025-08-02 21:59:57,279 - __main__ - INFO - ------------------------------
2025-08-02 21:59:57,279 - __main__ - INFO - === Testing Data Loading ===
2025-08-02 21:59:57,704 - src.utils.openbb_helpers - WARNING - OpenBB not available, some features will be limited
2025-08-02 21:59:57,705 - __main__ - ERROR - ❌ Data loading failed: No module named 'data.data_loader'
2025-08-02 21:59:57,705 - __main__ - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Desktop/VS code file/test-1-open-bb-/debug_simulator.py", line 104, in test_data_loading
    from data.data_loader import DataLoader
ModuleNotFoundError: No module named 'data.data_loader'

2025-08-02 21:59:57,705 - __main__ - INFO - ❌ Data Loading: FAILED
2025-08-02 21:59:57,705 - __main__ - INFO - 
🧪 Running: Momentum Strategy
2025-08-02 21:59:57,705 - __main__ - INFO - ------------------------------
2025-08-02 21:59:57,705 - __main__ - INFO - === Testing Momentum Strategy ===
2025-08-02 21:59:57,708 - __main__ - ERROR - ❌ Momentum strategy failed: No module named 'strategies.momentum'
2025-08-02 21:59:57,708 - __main__ - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Desktop/VS code file/test-1-open-bb-/debug_simulator.py", line 133, in test_momentum_strategy
    from strategies.momentum import SimpleMomentumStrategy
ModuleNotFoundError: No module named 'strategies.momentum'

2025-08-02 21:59:57,708 - __main__ - INFO - ❌ Momentum Strategy: FAILED
2025-08-02 21:59:57,708 - __main__ - INFO - 
🧪 Running: Simple Simulation
2025-08-02 21:59:57,708 - __main__ - INFO - ------------------------------
2025-08-02 21:59:57,708 - __main__ - INFO - === Running Simple Simulation ===
2025-08-02 21:59:57,709 - __main__ - ERROR - ❌ Simple simulation failed: attempted relative import beyond top-level package
2025-08-02 21:59:57,709 - __main__ - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Desktop/VS code file/test-1-open-bb-/debug_simulator.py", line 176, in run_simple_simulation
    from simulation.simulation_engine import SimulationEngine, SimulationConfig
  File "/Users/<USER>/Desktop/VS code file/test-1-open-bb-/src/simulation/__init__.py", line 4, in <module>
    from .simulation_engine import SimulationEngine
  File "/Users/<USER>/Desktop/VS code file/test-1-open-bb-/src/simulation/simulation_engine.py", line 13, in <module>
    from ..strategies.base_strategy import BaseStrategy
ImportError: attempted relative import beyond top-level package

2025-08-02 21:59:57,709 - __main__ - INFO - ❌ Simple Simulation: FAILED
2025-08-02 21:59:57,709 - __main__ - INFO - 
==================================================
2025-08-02 21:59:57,709 - __main__ - INFO - 🎯 DEBUG SUMMARY
2025-08-02 21:59:57,709 - __main__ - INFO - ==================================================
2025-08-02 21:59:57,709 - __main__ - INFO - ❌ FAIL: Portfolio Initialization
2025-08-02 21:59:57,709 - __main__ - INFO - ❌ FAIL: Simulation Engine
2025-08-02 21:59:57,709 - __main__ - INFO - ❌ FAIL: Data Loading
2025-08-02 21:59:57,709 - __main__ - INFO - ❌ FAIL: Momentum Strategy
2025-08-02 21:59:57,709 - __main__ - INFO - ❌ FAIL: Simple Simulation
2025-08-02 21:59:57,709 - __main__ - INFO - 
Overall: 0/5 tests passed
2025-08-02 21:59:57,709 - __main__ - INFO - 🔧 Some tests failed. Check logs above for details.
2025-08-02 22:01:10,052 - __main__ - INFO - 🔧 Starting Debug Session
2025-08-02 22:01:10,053 - __main__ - INFO - ==================================================
2025-08-02 22:01:10,053 - __main__ - INFO - 
🧪 Running: Portfolio Initialization
2025-08-02 22:01:10,053 - __main__ - INFO - ------------------------------
2025-08-02 22:01:10,053 - __main__ - INFO - === Testing Portfolio Initialization ===
2025-08-02 22:01:10,665 - matplotlib - DEBUG - matplotlib data path: /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/matplotlib/mpl-data
2025-08-02 22:01:10,669 - matplotlib - DEBUG - CONFIGDIR=/Users/<USER>/.matplotlib
2025-08-02 22:01:10,670 - matplotlib - DEBUG - interactive is False
2025-08-02 22:01:10,670 - matplotlib - DEBUG - platform is darwin
2025-08-02 22:01:10,702 - matplotlib - DEBUG - CACHEDIR=/Users/<USER>/.matplotlib
2025-08-02 22:01:10,704 - matplotlib.font_manager - DEBUG - Using fontManager instance from /Users/<USER>/.matplotlib/fontlist-v390.json
2025-08-02 22:01:11,676 - __main__ - INFO - Testing portfolio with $10,000.00
2025-08-02 22:01:11,676 - __main__ - INFO - Portfolio created:
2025-08-02 22:01:11,676 - __main__ - INFO -   Initial Capital: $10,000.00
2025-08-02 22:01:11,676 - __main__ - INFO -   Current Cash: $10,000.00
2025-08-02 22:01:11,676 - __main__ - INFO -   Positions: 0
2025-08-02 22:01:11,676 - __main__ - INFO -   Portfolio Value: $10,000.00
2025-08-02 22:01:11,676 - __main__ - INFO - ✅ Portfolio test passed for $10,000.00
2025-08-02 22:01:11,676 - __main__ - INFO - Testing portfolio with $100,000.00
2025-08-02 22:01:11,676 - __main__ - INFO - Portfolio created:
2025-08-02 22:01:11,676 - __main__ - INFO -   Initial Capital: $100,000.00
2025-08-02 22:01:11,676 - __main__ - INFO -   Current Cash: $100,000.00
2025-08-02 22:01:11,676 - __main__ - INFO -   Positions: 0
2025-08-02 22:01:11,676 - __main__ - INFO -   Portfolio Value: $100,000.00
2025-08-02 22:01:11,676 - __main__ - INFO - ✅ Portfolio test passed for $100,000.00
2025-08-02 22:01:11,676 - __main__ - INFO - Testing portfolio with $50,000.00
2025-08-02 22:01:11,676 - __main__ - INFO - Portfolio created:
2025-08-02 22:01:11,676 - __main__ - INFO -   Initial Capital: $50,000.00
2025-08-02 22:01:11,676 - __main__ - INFO -   Current Cash: $50,000.00
2025-08-02 22:01:11,677 - __main__ - INFO -   Positions: 0
2025-08-02 22:01:11,677 - __main__ - INFO -   Portfolio Value: $50,000.00
2025-08-02 22:01:11,677 - __main__ - INFO - ✅ Portfolio test passed for $50,000.00
2025-08-02 22:01:11,677 - __main__ - INFO - ✅ Portfolio Initialization: PASSED
2025-08-02 22:01:11,677 - __main__ - INFO - 
🧪 Running: Simulation Engine
2025-08-02 22:01:11,677 - __main__ - INFO - ------------------------------
2025-08-02 22:01:11,677 - __main__ - INFO - === Testing Simulation Engine ===
2025-08-02 22:01:11,677 - __main__ - INFO - Config created: $100,000.00
2025-08-02 22:01:11,677 - src.simulation.simulation_engine - INFO - SimulationEngine: Creating portfolio with $100,000.00
2025-08-02 22:01:11,677 - src.simulation.simulation_engine - INFO - SimulationEngine: Portfolio created with $100,000.00 cash
2025-08-02 22:01:11,677 - src.simulation.simulation_engine - INFO - Simulation engine initialized with $100,000.00 capital
2025-08-02 22:01:11,677 - __main__ - INFO - Engine created:
2025-08-02 22:01:11,677 - __main__ - INFO -   Portfolio Cash: $100,000.00
2025-08-02 22:01:11,677 - __main__ - INFO -   Portfolio Initial: $100,000.00
2025-08-02 22:01:11,677 - __main__ - INFO - ✅ Simulation engine test passed
2025-08-02 22:01:11,677 - __main__ - INFO - ✅ Simulation Engine: PASSED
2025-08-02 22:01:11,677 - __main__ - INFO - 
🧪 Running: Data Loading
2025-08-02 22:01:11,677 - __main__ - INFO - ------------------------------
2025-08-02 22:01:11,677 - __main__ - INFO - === Testing Data Loading ===
2025-08-02 22:01:11,991 - src.utils.openbb_helpers - WARNING - OpenBB not available, some features will be limited
2025-08-02 22:01:11,992 - __main__ - ERROR - ❌ Data loading failed: No module named 'src.data.data_loader'
2025-08-02 22:01:11,993 - __main__ - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Desktop/VS code file/test-1-open-bb-/debug_simulator.py", line 105, in test_data_loading
    from src.data.data_loader import DataLoader
ModuleNotFoundError: No module named 'src.data.data_loader'

2025-08-02 22:01:11,993 - __main__ - INFO - ❌ Data Loading: FAILED
2025-08-02 22:01:11,993 - __main__ - INFO - 
🧪 Running: Momentum Strategy
2025-08-02 22:01:11,993 - __main__ - INFO - ------------------------------
2025-08-02 22:01:11,993 - __main__ - INFO - === Testing Momentum Strategy ===
2025-08-02 22:01:11,993 - __main__ - ERROR - ❌ Momentum strategy failed: No module named 'src.strategies.momentum'
2025-08-02 22:01:11,993 - __main__ - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Desktop/VS code file/test-1-open-bb-/debug_simulator.py", line 134, in test_momentum_strategy
    from src.strategies.momentum import SimpleMomentumStrategy
ModuleNotFoundError: No module named 'src.strategies.momentum'

2025-08-02 22:01:11,993 - __main__ - INFO - ❌ Momentum Strategy: FAILED
2025-08-02 22:01:11,994 - __main__ - INFO - 
🧪 Running: Simple Simulation
2025-08-02 22:01:11,994 - __main__ - INFO - ------------------------------
2025-08-02 22:01:11,994 - __main__ - INFO - === Running Simple Simulation ===
2025-08-02 22:01:11,994 - __main__ - ERROR - ❌ Simple simulation failed: No module named 'src.strategies.momentum'
2025-08-02 22:01:11,994 - __main__ - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Desktop/VS code file/test-1-open-bb-/debug_simulator.py", line 178, in run_simple_simulation
    from src.strategies.momentum import SimpleMomentumStrategy
ModuleNotFoundError: No module named 'src.strategies.momentum'

2025-08-02 22:01:11,994 - __main__ - INFO - ❌ Simple Simulation: FAILED
2025-08-02 22:01:11,994 - __main__ - INFO - 
==================================================
2025-08-02 22:01:11,994 - __main__ - INFO - 🎯 DEBUG SUMMARY
2025-08-02 22:01:11,994 - __main__ - INFO - ==================================================
2025-08-02 22:01:11,994 - __main__ - INFO - ✅ PASS: Portfolio Initialization
2025-08-02 22:01:11,994 - __main__ - INFO - ✅ PASS: Simulation Engine
2025-08-02 22:01:11,994 - __main__ - INFO - ❌ FAIL: Data Loading
2025-08-02 22:01:11,994 - __main__ - INFO - ❌ FAIL: Momentum Strategy
2025-08-02 22:01:11,994 - __main__ - INFO - ❌ FAIL: Simple Simulation
2025-08-02 22:01:11,994 - __main__ - INFO - 
Overall: 2/5 tests passed
2025-08-02 22:01:11,994 - __main__ - INFO - 🔧 Some tests failed. Check logs above for details.
2025-08-02 22:01:50,156 - __main__ - INFO - 🔧 Starting Debug Session
2025-08-02 22:01:50,156 - __main__ - INFO - ==================================================
2025-08-02 22:01:50,157 - __main__ - INFO - 
🧪 Running: Portfolio Initialization
2025-08-02 22:01:50,157 - __main__ - INFO - ------------------------------
2025-08-02 22:01:50,157 - __main__ - INFO - === Testing Portfolio Initialization ===
2025-08-02 22:01:50,746 - matplotlib - DEBUG - matplotlib data path: /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/matplotlib/mpl-data
2025-08-02 22:01:50,749 - matplotlib - DEBUG - CONFIGDIR=/Users/<USER>/.matplotlib
2025-08-02 22:01:50,749 - matplotlib - DEBUG - interactive is False
2025-08-02 22:01:50,749 - matplotlib - DEBUG - platform is darwin
2025-08-02 22:01:50,781 - matplotlib - DEBUG - CACHEDIR=/Users/<USER>/.matplotlib
2025-08-02 22:01:50,783 - matplotlib.font_manager - DEBUG - Using fontManager instance from /Users/<USER>/.matplotlib/fontlist-v390.json
2025-08-02 22:01:51,669 - __main__ - INFO - Testing portfolio with $10,000.00
2025-08-02 22:01:51,669 - __main__ - INFO - Portfolio created:
2025-08-02 22:01:51,669 - __main__ - INFO -   Initial Capital: $10,000.00
2025-08-02 22:01:51,669 - __main__ - INFO -   Current Cash: $10,000.00
2025-08-02 22:01:51,669 - __main__ - INFO -   Positions: 0
2025-08-02 22:01:51,670 - __main__ - INFO -   Portfolio Value: $10,000.00
2025-08-02 22:01:51,670 - __main__ - INFO - ✅ Portfolio test passed for $10,000.00
2025-08-02 22:01:51,670 - __main__ - INFO - Testing portfolio with $100,000.00
2025-08-02 22:01:51,670 - __main__ - INFO - Portfolio created:
2025-08-02 22:01:51,670 - __main__ - INFO -   Initial Capital: $100,000.00
2025-08-02 22:01:51,670 - __main__ - INFO -   Current Cash: $100,000.00
2025-08-02 22:01:51,670 - __main__ - INFO -   Positions: 0
2025-08-02 22:01:51,670 - __main__ - INFO -   Portfolio Value: $100,000.00
2025-08-02 22:01:51,670 - __main__ - INFO - ✅ Portfolio test passed for $100,000.00
2025-08-02 22:01:51,670 - __main__ - INFO - Testing portfolio with $50,000.00
2025-08-02 22:01:51,670 - __main__ - INFO - Portfolio created:
2025-08-02 22:01:51,670 - __main__ - INFO -   Initial Capital: $50,000.00
2025-08-02 22:01:51,670 - __main__ - INFO -   Current Cash: $50,000.00
2025-08-02 22:01:51,670 - __main__ - INFO -   Positions: 0
2025-08-02 22:01:51,670 - __main__ - INFO -   Portfolio Value: $50,000.00
2025-08-02 22:01:51,670 - __main__ - INFO - ✅ Portfolio test passed for $50,000.00
2025-08-02 22:01:51,670 - __main__ - INFO - ✅ Portfolio Initialization: PASSED
2025-08-02 22:01:51,670 - __main__ - INFO - 
🧪 Running: Simulation Engine
2025-08-02 22:01:51,670 - __main__ - INFO - ------------------------------
2025-08-02 22:01:51,670 - __main__ - INFO - === Testing Simulation Engine ===
2025-08-02 22:01:51,670 - __main__ - INFO - Config created: $100,000.00
2025-08-02 22:01:51,670 - src.simulation.simulation_engine - INFO - SimulationEngine: Creating portfolio with $100,000.00
2025-08-02 22:01:51,670 - src.simulation.simulation_engine - INFO - SimulationEngine: Portfolio created with $100,000.00 cash
2025-08-02 22:01:51,670 - src.simulation.simulation_engine - INFO - Simulation engine initialized with $100,000.00 capital
2025-08-02 22:01:51,670 - __main__ - INFO - Engine created:
2025-08-02 22:01:51,670 - __main__ - INFO -   Portfolio Cash: $100,000.00
2025-08-02 22:01:51,670 - __main__ - INFO -   Portfolio Initial: $100,000.00
2025-08-02 22:01:51,670 - __main__ - INFO - ✅ Simulation engine test passed
2025-08-02 22:01:51,670 - __main__ - INFO - ✅ Simulation Engine: PASSED
2025-08-02 22:01:51,670 - __main__ - INFO - 
🧪 Running: Data Loading
2025-08-02 22:01:51,670 - __main__ - INFO - ------------------------------
2025-08-02 22:01:51,670 - __main__ - INFO - === Testing Data Loading ===
2025-08-02 22:01:52,017 - src.utils.openbb_helpers - WARNING - OpenBB not available, some features will be limited
2025-08-02 22:01:52,018 - __main__ - ERROR - ❌ Data loading failed: No module named 'src.data.data_loader'
2025-08-02 22:01:52,018 - __main__ - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Desktop/VS code file/test-1-open-bb-/debug_simulator.py", line 105, in test_data_loading
    from src.data.data_loader import DataLoader
ModuleNotFoundError: No module named 'src.data.data_loader'

2025-08-02 22:01:52,018 - __main__ - INFO - ❌ Data Loading: FAILED
2025-08-02 22:01:52,018 - __main__ - INFO - 
🧪 Running: Momentum Strategy
2025-08-02 22:01:52,019 - __main__ - INFO - ------------------------------
2025-08-02 22:01:52,019 - __main__ - INFO - === Testing Momentum Strategy ===
2025-08-02 22:01:52,019 - __main__ - ERROR - ❌ Momentum strategy failed: No module named 'src.data.data_loader'
2025-08-02 22:01:52,019 - __main__ - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Desktop/VS code file/test-1-open-bb-/debug_simulator.py", line 135, in test_momentum_strategy
    from src.data.data_loader import DataLoader
ModuleNotFoundError: No module named 'src.data.data_loader'

2025-08-02 22:01:52,019 - __main__ - INFO - ❌ Momentum Strategy: FAILED
2025-08-02 22:01:52,019 - __main__ - INFO - 
🧪 Running: Simple Simulation
2025-08-02 22:01:52,019 - __main__ - INFO - ------------------------------
2025-08-02 22:01:52,019 - __main__ - INFO - === Running Simple Simulation ===
2025-08-02 22:01:52,019 - __main__ - ERROR - ❌ Simple simulation failed: No module named 'src.data.data_loader'
2025-08-02 22:01:52,019 - __main__ - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Desktop/VS code file/test-1-open-bb-/debug_simulator.py", line 179, in run_simple_simulation
    from src.data.data_loader import DataLoader
ModuleNotFoundError: No module named 'src.data.data_loader'

2025-08-02 22:01:52,019 - __main__ - INFO - ❌ Simple Simulation: FAILED
2025-08-02 22:01:52,019 - __main__ - INFO - 
==================================================
2025-08-02 22:01:52,019 - __main__ - INFO - 🎯 DEBUG SUMMARY
2025-08-02 22:01:52,019 - __main__ - INFO - ==================================================
2025-08-02 22:01:52,019 - __main__ - INFO - ✅ PASS: Portfolio Initialization
2025-08-02 22:01:52,019 - __main__ - INFO - ✅ PASS: Simulation Engine
2025-08-02 22:01:52,019 - __main__ - INFO - ❌ FAIL: Data Loading
2025-08-02 22:01:52,019 - __main__ - INFO - ❌ FAIL: Momentum Strategy
2025-08-02 22:01:52,019 - __main__ - INFO - ❌ FAIL: Simple Simulation
2025-08-02 22:01:52,019 - __main__ - INFO - 
Overall: 2/5 tests passed
2025-08-02 22:01:52,019 - __main__ - INFO - 🔧 Some tests failed. Check logs above for details.
2025-08-02 22:02:59,114 - __main__ - INFO - 🔧 Starting Debug Session
2025-08-02 22:02:59,114 - __main__ - INFO - ==================================================
2025-08-02 22:02:59,114 - __main__ - INFO - 
🧪 Running: Portfolio Initialization
2025-08-02 22:02:59,114 - __main__ - INFO - ------------------------------
2025-08-02 22:02:59,114 - __main__ - INFO - === Testing Portfolio Initialization ===
2025-08-02 22:02:59,699 - matplotlib - DEBUG - matplotlib data path: /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/matplotlib/mpl-data
2025-08-02 22:02:59,702 - matplotlib - DEBUG - CONFIGDIR=/Users/<USER>/.matplotlib
2025-08-02 22:02:59,702 - matplotlib - DEBUG - interactive is False
2025-08-02 22:02:59,703 - matplotlib - DEBUG - platform is darwin
2025-08-02 22:02:59,735 - matplotlib - DEBUG - CACHEDIR=/Users/<USER>/.matplotlib
2025-08-02 22:02:59,737 - matplotlib.font_manager - DEBUG - Using fontManager instance from /Users/<USER>/.matplotlib/fontlist-v390.json
2025-08-02 22:03:00,675 - __main__ - INFO - Testing portfolio with $10,000.00
2025-08-02 22:03:00,675 - __main__ - INFO - Portfolio created:
2025-08-02 22:03:00,675 - __main__ - INFO -   Initial Capital: $10,000.00
2025-08-02 22:03:00,675 - __main__ - INFO -   Current Cash: $10,000.00
2025-08-02 22:03:00,675 - __main__ - INFO -   Positions: 0
2025-08-02 22:03:00,675 - __main__ - INFO -   Portfolio Value: $10,000.00
2025-08-02 22:03:00,675 - __main__ - INFO - ✅ Portfolio test passed for $10,000.00
2025-08-02 22:03:00,675 - __main__ - INFO - Testing portfolio with $100,000.00
2025-08-02 22:03:00,675 - __main__ - INFO - Portfolio created:
2025-08-02 22:03:00,675 - __main__ - INFO -   Initial Capital: $100,000.00
2025-08-02 22:03:00,676 - __main__ - INFO -   Current Cash: $100,000.00
2025-08-02 22:03:00,676 - __main__ - INFO -   Positions: 0
2025-08-02 22:03:00,676 - __main__ - INFO -   Portfolio Value: $100,000.00
2025-08-02 22:03:00,676 - __main__ - INFO - ✅ Portfolio test passed for $100,000.00
2025-08-02 22:03:00,676 - __main__ - INFO - Testing portfolio with $50,000.00
2025-08-02 22:03:00,676 - __main__ - INFO - Portfolio created:
2025-08-02 22:03:00,676 - __main__ - INFO -   Initial Capital: $50,000.00
2025-08-02 22:03:00,676 - __main__ - INFO -   Current Cash: $50,000.00
2025-08-02 22:03:00,676 - __main__ - INFO -   Positions: 0
2025-08-02 22:03:00,676 - __main__ - INFO -   Portfolio Value: $50,000.00
2025-08-02 22:03:00,676 - __main__ - INFO - ✅ Portfolio test passed for $50,000.00
2025-08-02 22:03:00,676 - __main__ - INFO - ✅ Portfolio Initialization: PASSED
2025-08-02 22:03:00,676 - __main__ - INFO - 
🧪 Running: Simulation Engine
2025-08-02 22:03:00,676 - __main__ - INFO - ------------------------------
2025-08-02 22:03:00,676 - __main__ - INFO - === Testing Simulation Engine ===
2025-08-02 22:03:00,676 - __main__ - INFO - Config created: $100,000.00
2025-08-02 22:03:00,676 - src.simulation.simulation_engine - INFO - SimulationEngine: Creating portfolio with $100,000.00
2025-08-02 22:03:00,676 - src.simulation.simulation_engine - INFO - SimulationEngine: Portfolio created with $100,000.00 cash
2025-08-02 22:03:00,676 - src.simulation.simulation_engine - INFO - Simulation engine initialized with $100,000.00 capital
2025-08-02 22:03:00,676 - __main__ - INFO - Engine created:
2025-08-02 22:03:00,676 - __main__ - INFO -   Portfolio Cash: $100,000.00
2025-08-02 22:03:00,676 - __main__ - INFO -   Portfolio Initial: $100,000.00
2025-08-02 22:03:00,676 - __main__ - INFO - ✅ Simulation engine test passed
2025-08-02 22:03:00,676 - __main__ - INFO - ✅ Simulation Engine: PASSED
2025-08-02 22:03:00,676 - __main__ - INFO - 
🧪 Running: Data Loading
2025-08-02 22:03:00,676 - __main__ - INFO - ------------------------------
2025-08-02 22:03:00,676 - __main__ - INFO - === Testing Data Loading ===
2025-08-02 22:03:01,027 - src.utils.openbb_helpers - WARNING - OpenBB not available, some features will be limited
2025-08-02 22:03:01,032 - __main__ - INFO - OpenBBDataClient created
2025-08-02 22:03:01,032 - src.data.openbb_client - INFO - Fetching BTC-USD data from yfinance
2025-08-02 22:03:01,032 - yfinance - DEBUG - Entering history()
2025-08-02 22:03:01,036 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-08-02 22:03:01,036 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['BTC-USD', 1, 0])
2025-08-02 22:03:01,036 - yfinance - DEBUG -  Entering history()
2025-08-02 22:03:01,037 - yfinance - DEBUG - BTC-USD: Yahoo GET parameters: {'period1': '2025-07-03 22:03:01+00:00', 'period2': '2025-08-02 22:03:01+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-08-02 22:03:01,038 - yfinance - DEBUG -   Entering get()
2025-08-02 22:03:01,038 - yfinance - DEBUG -    Entering _make_request()
2025-08-02 22:03:01,038 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/BTC-USD
2025-08-02 22:03:01,038 - yfinance - DEBUG - params={'period1': 1751580181, 'period2': 1754172181, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-08-02 22:03:01,038 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-08-02 22:03:01,039 - yfinance - DEBUG - cookie_mode = 'basic'
2025-08-02 22:03:01,039 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-08-02 22:03:01,039 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-08-02 22:03:01,039 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-08-02 22:03:01,040 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-08-02 22:03:01,040 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-08-02 22:03:01,040 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-08-02 22:03:01,040 - yfinance - DEBUG - reusing persistent cookie
2025-08-02 22:03:01,040 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-08-02 22:03:01,040 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-08-02 22:03:01,040 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-08-02 22:03:01,040 - yfinance - DEBUG - reusing cookie
2025-08-02 22:03:01,040 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-08-02 22:03:01,301 - yfinance - DEBUG - crumb = '8Iqp7FcY9f0'
2025-08-02 22:03:01,301 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-08-02 22:03:01,301 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-08-02 22:03:01,301 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-08-02 22:03:01,355 - yfinance - DEBUG - response code=200
2025-08-02 22:03:01,355 - yfinance - DEBUG -    Exiting _make_request()
2025-08-02 22:03:01,355 - yfinance - DEBUG -   Exiting get()
2025-08-02 22:03:01,360 - yfinance - DEBUG - BTC-USD: yfinance received OHLC data: 2025-07-03 00:00:00 -> 2025-08-02 14:00:00
2025-08-02 22:03:01,364 - yfinance - DEBUG - BTC-USD: OHLC after cleaning: 2025-07-03 00:00:00+00:00 -> 2025-08-02 14:00:00+00:00
2025-08-02 22:03:01,370 - yfinance - DEBUG - BTC-USD: OHLC after combining events: 2025-07-03 00:00:00+00:00 -> 2025-08-02 00:00:00+00:00
2025-08-02 22:03:01,376 - yfinance - DEBUG - BTC-USD: yfinance returning OHLC: 2025-07-03 00:00:00+00:00 -> 2025-08-02 00:00:00+00:00
2025-08-02 22:03:01,376 - yfinance - DEBUG -  Exiting history()
2025-08-02 22:03:01,376 - yfinance - DEBUG - Exiting history()
2025-08-02 22:03:01,376 - src.data.openbb_client - INFO - Successfully fetched 30 records
2025-08-02 22:03:01,377 - __main__ - INFO - ✅ Data loaded: 30 rows
2025-08-02 22:03:01,377 - __main__ - INFO -   Date range: 2025-07-03 00:00:00+00:00 to 2025-08-02 00:00:00+00:00
2025-08-02 22:03:01,377 - __main__ - INFO -   Columns: ['open', 'high', 'low', 'close', 'volume', 'Dividends', 'Stock Splits']
2025-08-02 22:03:01,377 - __main__ - INFO -   Price range: $108034.34 - $119995.41
2025-08-02 22:03:01,377 - __main__ - INFO - ✅ Data Loading: PASSED
2025-08-02 22:03:01,377 - __main__ - INFO - 
🧪 Running: Momentum Strategy
2025-08-02 22:03:01,377 - __main__ - INFO - ------------------------------
2025-08-02 22:03:01,377 - __main__ - INFO - === Testing Momentum Strategy ===
2025-08-02 22:03:01,377 - __main__ - INFO - Momentum strategy created
2025-08-02 22:03:01,382 - src.data.openbb_client - INFO - Fetching BTC-USD data from yfinance
2025-08-02 22:03:01,382 - yfinance - DEBUG - Entering history()
2025-08-02 22:03:01,383 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['BTC-USD', 1, 0])
2025-08-02 22:03:01,383 - yfinance - DEBUG -  Entering history()
2025-08-02 22:03:01,383 - yfinance - DEBUG - BTC-USD: Yahoo GET parameters: {'period1': '2025-07-03 22:03:01+00:00', 'period2': '2025-08-02 22:03:01+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-08-02 22:03:01,383 - yfinance - DEBUG -   Entering get()
2025-08-02 22:03:01,383 - yfinance - DEBUG -    Entering _make_request()
2025-08-02 22:03:01,383 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/BTC-USD
2025-08-02 22:03:01,383 - yfinance - DEBUG - params={'period1': 1751580181, 'period2': 1754172181, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-08-02 22:03:01,383 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-08-02 22:03:01,383 - yfinance - DEBUG - cookie_mode = 'basic'
2025-08-02 22:03:01,383 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-08-02 22:03:01,383 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-08-02 22:03:01,383 - yfinance - DEBUG - reusing cookie
2025-08-02 22:03:01,383 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-08-02 22:03:01,383 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-08-02 22:03:01,383 - yfinance - DEBUG - reusing crumb
2025-08-02 22:03:01,383 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-08-02 22:03:01,383 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-08-02 22:03:01,383 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-08-02 22:03:01,400 - yfinance - DEBUG - response code=200
2025-08-02 22:03:01,401 - yfinance - DEBUG -    Exiting _make_request()
2025-08-02 22:03:01,401 - yfinance - DEBUG -   Exiting get()
2025-08-02 22:03:01,401 - yfinance - DEBUG - BTC-USD: yfinance received OHLC data: 2025-07-03 00:00:00 -> 2025-08-02 14:00:00
2025-08-02 22:03:01,402 - yfinance - DEBUG - BTC-USD: OHLC after cleaning: 2025-07-03 00:00:00+00:00 -> 2025-08-02 14:00:00+00:00
2025-08-02 22:03:01,404 - yfinance - DEBUG - BTC-USD: OHLC after combining events: 2025-07-03 00:00:00+00:00 -> 2025-08-02 00:00:00+00:00
2025-08-02 22:03:01,407 - yfinance - DEBUG - BTC-USD: yfinance returning OHLC: 2025-07-03 00:00:00+00:00 -> 2025-08-02 00:00:00+00:00
2025-08-02 22:03:01,407 - yfinance - DEBUG -  Exiting history()
2025-08-02 22:03:01,407 - yfinance - DEBUG - Exiting history()
2025-08-02 22:03:01,408 - src.data.openbb_client - INFO - Successfully fetched 30 records
2025-08-02 22:03:01,408 - __main__ - ERROR - ❌ Momentum strategy failed: 'SimpleMomentumStrategy' object has no attribute 'generate_signal'
2025-08-02 22:03:01,409 - __main__ - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Desktop/VS code file/test-1-open-bb-/debug_simulator.py", line 168, in test_momentum_strategy
    signal = strategy.generate_signal(current_data)
             ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SimpleMomentumStrategy' object has no attribute 'generate_signal'. Did you mean: 'generate_signals'?

2025-08-02 22:03:01,409 - __main__ - INFO - ❌ Momentum Strategy: FAILED
2025-08-02 22:03:01,409 - __main__ - INFO - 
🧪 Running: Simple Simulation
2025-08-02 22:03:01,409 - __main__ - INFO - ------------------------------
2025-08-02 22:03:01,409 - __main__ - INFO - === Running Simple Simulation ===
2025-08-02 22:03:01,410 - src.simulation.simulation_engine - INFO - SimulationEngine: Creating portfolio with $100,000.00
2025-08-02 22:03:01,410 - src.simulation.simulation_engine - INFO - SimulationEngine: Portfolio created with $100,000.00 cash
2025-08-02 22:03:01,410 - src.simulation.simulation_engine - INFO - Simulation engine initialized with $100,000.00 capital
2025-08-02 22:03:01,410 - __main__ - INFO - Engine initialized with $100,000.00
2025-08-02 22:03:01,414 - src.data.openbb_client - INFO - Fetching BTC-USD data from yfinance
2025-08-02 22:03:01,414 - yfinance - DEBUG - Entering history()
2025-08-02 22:03:01,414 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['BTC-USD', 1, 0])
2025-08-02 22:03:01,414 - yfinance - DEBUG -  Entering history()
2025-08-02 22:03:01,415 - yfinance - DEBUG - BTC-USD: Yahoo GET parameters: {'period1': '2025-07-03 22:03:01+00:00', 'period2': '2025-08-02 22:03:01+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-08-02 22:03:01,415 - yfinance - DEBUG -   Entering get()
2025-08-02 22:03:01,415 - yfinance - DEBUG -    Entering _make_request()
2025-08-02 22:03:01,415 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/BTC-USD
2025-08-02 22:03:01,415 - yfinance - DEBUG - params={'period1': 1751580181, 'period2': 1754172181, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-08-02 22:03:01,415 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-08-02 22:03:01,415 - yfinance - DEBUG - cookie_mode = 'basic'
2025-08-02 22:03:01,415 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-08-02 22:03:01,415 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-08-02 22:03:01,415 - yfinance - DEBUG - reusing cookie
2025-08-02 22:03:01,415 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-08-02 22:03:01,415 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-08-02 22:03:01,415 - yfinance - DEBUG - reusing crumb
2025-08-02 22:03:01,415 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-08-02 22:03:01,415 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-08-02 22:03:01,415 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-08-02 22:03:01,427 - yfinance - DEBUG - response code=200
2025-08-02 22:03:01,427 - yfinance - DEBUG -    Exiting _make_request()
2025-08-02 22:03:01,427 - yfinance - DEBUG -   Exiting get()
2025-08-02 22:03:01,428 - yfinance - DEBUG - BTC-USD: yfinance received OHLC data: 2025-07-03 00:00:00 -> 2025-08-02 14:00:00
2025-08-02 22:03:01,428 - yfinance - DEBUG - BTC-USD: OHLC after cleaning: 2025-07-03 00:00:00+00:00 -> 2025-08-02 14:00:00+00:00
2025-08-02 22:03:01,430 - yfinance - DEBUG - BTC-USD: OHLC after combining events: 2025-07-03 00:00:00+00:00 -> 2025-08-02 00:00:00+00:00
2025-08-02 22:03:01,432 - yfinance - DEBUG - BTC-USD: yfinance returning OHLC: 2025-07-03 00:00:00+00:00 -> 2025-08-02 00:00:00+00:00
2025-08-02 22:03:01,432 - yfinance - DEBUG -  Exiting history()
2025-08-02 22:03:01,432 - yfinance - DEBUG - Exiting history()
2025-08-02 22:03:01,433 - src.data.openbb_client - INFO - Successfully fetched 30 records
2025-08-02 22:03:01,433 - __main__ - INFO - Data loaded: 30 rows
2025-08-02 22:03:01,433 - src.simulation.simulation_engine - INFO - Loaded 30 data points from 2025-07-03 00:00:00+00:00 to 2025-08-02 00:00:00+00:00
2025-08-02 22:03:01,433 - src.simulation.simulation_engine - INFO - Strategy set: SimpleMomentumStrategy
2025-08-02 22:03:01,433 - __main__ - INFO - Before simulation - Cash: $100,000.00
2025-08-02 22:03:01,433 - __main__ - INFO - 
--- Step 1 (index 15) ---
2025-08-02 22:03:01,433 - __main__ - INFO - Price: $118003.23
2025-08-02 22:03:01,433 - __main__ - INFO - Cash before: $100000.00
2025-08-02 22:03:01,433 - __main__ - ERROR - ❌ Simple simulation failed: 'SimpleMomentumStrategy' object has no attribute 'generate_signal'
2025-08-02 22:03:01,434 - __main__ - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Desktop/VS code file/test-1-open-bb-/debug_simulator.py", line 246, in run_simple_simulation
    signal = strategy.generate_signal(current_data)
             ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SimpleMomentumStrategy' object has no attribute 'generate_signal'. Did you mean: 'generate_signals'?

2025-08-02 22:03:01,434 - __main__ - INFO - ❌ Simple Simulation: FAILED
2025-08-02 22:03:01,434 - __main__ - INFO - 
==================================================
2025-08-02 22:03:01,434 - __main__ - INFO - 🎯 DEBUG SUMMARY
2025-08-02 22:03:01,434 - __main__ - INFO - ==================================================
2025-08-02 22:03:01,434 - __main__ - INFO - ✅ PASS: Portfolio Initialization
2025-08-02 22:03:01,434 - __main__ - INFO - ✅ PASS: Simulation Engine
2025-08-02 22:03:01,434 - __main__ - INFO - ✅ PASS: Data Loading
2025-08-02 22:03:01,434 - __main__ - INFO - ❌ FAIL: Momentum Strategy
2025-08-02 22:03:01,434 - __main__ - INFO - ❌ FAIL: Simple Simulation
2025-08-02 22:03:01,434 - __main__ - INFO - 
Overall: 3/5 tests passed
2025-08-02 22:03:01,434 - __main__ - INFO - 🔧 Some tests failed. Check logs above for details.
2025-08-02 22:03:36,259 - __main__ - INFO - 🔧 Starting Debug Session
2025-08-02 22:03:36,260 - __main__ - INFO - ==================================================
2025-08-02 22:03:36,260 - __main__ - INFO - 
🧪 Running: Portfolio Initialization
2025-08-02 22:03:36,260 - __main__ - INFO - ------------------------------
2025-08-02 22:03:36,260 - __main__ - INFO - === Testing Portfolio Initialization ===
2025-08-02 22:03:36,831 - matplotlib - DEBUG - matplotlib data path: /Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/matplotlib/mpl-data
2025-08-02 22:03:36,834 - matplotlib - DEBUG - CONFIGDIR=/Users/<USER>/.matplotlib
2025-08-02 22:03:36,834 - matplotlib - DEBUG - interactive is False
2025-08-02 22:03:36,834 - matplotlib - DEBUG - platform is darwin
2025-08-02 22:03:36,867 - matplotlib - DEBUG - CACHEDIR=/Users/<USER>/.matplotlib
2025-08-02 22:03:36,869 - matplotlib.font_manager - DEBUG - Using fontManager instance from /Users/<USER>/.matplotlib/fontlist-v390.json
2025-08-02 22:03:37,765 - __main__ - INFO - Testing portfolio with $10,000.00
2025-08-02 22:03:37,765 - __main__ - INFO - Portfolio created:
2025-08-02 22:03:37,765 - __main__ - INFO -   Initial Capital: $10,000.00
2025-08-02 22:03:37,765 - __main__ - INFO -   Current Cash: $10,000.00
2025-08-02 22:03:37,765 - __main__ - INFO -   Positions: 0
2025-08-02 22:03:37,765 - __main__ - INFO -   Portfolio Value: $10,000.00
2025-08-02 22:03:37,765 - __main__ - INFO - ✅ Portfolio test passed for $10,000.00
2025-08-02 22:03:37,765 - __main__ - INFO - Testing portfolio with $100,000.00
2025-08-02 22:03:37,765 - __main__ - INFO - Portfolio created:
2025-08-02 22:03:37,765 - __main__ - INFO -   Initial Capital: $100,000.00
2025-08-02 22:03:37,765 - __main__ - INFO -   Current Cash: $100,000.00
2025-08-02 22:03:37,765 - __main__ - INFO -   Positions: 0
2025-08-02 22:03:37,765 - __main__ - INFO -   Portfolio Value: $100,000.00
2025-08-02 22:03:37,765 - __main__ - INFO - ✅ Portfolio test passed for $100,000.00
2025-08-02 22:03:37,765 - __main__ - INFO - Testing portfolio with $50,000.00
2025-08-02 22:03:37,765 - __main__ - INFO - Portfolio created:
2025-08-02 22:03:37,765 - __main__ - INFO -   Initial Capital: $50,000.00
2025-08-02 22:03:37,765 - __main__ - INFO -   Current Cash: $50,000.00
2025-08-02 22:03:37,766 - __main__ - INFO -   Positions: 0
2025-08-02 22:03:37,766 - __main__ - INFO -   Portfolio Value: $50,000.00
2025-08-02 22:03:37,766 - __main__ - INFO - ✅ Portfolio test passed for $50,000.00
2025-08-02 22:03:37,766 - __main__ - INFO - ✅ Portfolio Initialization: PASSED
2025-08-02 22:03:37,766 - __main__ - INFO - 
🧪 Running: Simulation Engine
2025-08-02 22:03:37,766 - __main__ - INFO - ------------------------------
2025-08-02 22:03:37,766 - __main__ - INFO - === Testing Simulation Engine ===
2025-08-02 22:03:37,766 - __main__ - INFO - Config created: $100,000.00
2025-08-02 22:03:37,766 - src.simulation.simulation_engine - INFO - SimulationEngine: Creating portfolio with $100,000.00
2025-08-02 22:03:37,766 - src.simulation.simulation_engine - INFO - SimulationEngine: Portfolio created with $100,000.00 cash
2025-08-02 22:03:37,766 - src.simulation.simulation_engine - INFO - Simulation engine initialized with $100,000.00 capital
2025-08-02 22:03:37,766 - __main__ - INFO - Engine created:
2025-08-02 22:03:37,766 - __main__ - INFO -   Portfolio Cash: $100,000.00
2025-08-02 22:03:37,766 - __main__ - INFO -   Portfolio Initial: $100,000.00
2025-08-02 22:03:37,766 - __main__ - INFO - ✅ Simulation engine test passed
2025-08-02 22:03:37,766 - __main__ - INFO - ✅ Simulation Engine: PASSED
2025-08-02 22:03:37,766 - __main__ - INFO - 
🧪 Running: Data Loading
2025-08-02 22:03:37,766 - __main__ - INFO - ------------------------------
2025-08-02 22:03:37,766 - __main__ - INFO - === Testing Data Loading ===
2025-08-02 22:03:38,080 - src.utils.openbb_helpers - WARNING - OpenBB not available, some features will be limited
2025-08-02 22:03:38,084 - __main__ - INFO - OpenBBDataClient created
2025-08-02 22:03:38,084 - src.data.openbb_client - INFO - Fetching BTC-USD data from yfinance
2025-08-02 22:03:38,085 - yfinance - DEBUG - Entering history()
2025-08-02 22:03:38,088 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_tz_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-08-02 22:03:38,088 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['BTC-USD', 1, 0])
2025-08-02 22:03:38,088 - yfinance - DEBUG -  Entering history()
2025-08-02 22:03:38,090 - yfinance - DEBUG - BTC-USD: Yahoo GET parameters: {'period1': '2025-07-03 22:03:38+00:00', 'period2': '2025-08-02 22:03:38+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-08-02 22:03:38,090 - yfinance - DEBUG -   Entering get()
2025-08-02 22:03:38,090 - yfinance - DEBUG -    Entering _make_request()
2025-08-02 22:03:38,090 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/BTC-USD
2025-08-02 22:03:38,090 - yfinance - DEBUG - params={'period1': 1751580218, 'period2': 1754172218, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-08-02 22:03:38,090 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-08-02 22:03:38,091 - yfinance - DEBUG - cookie_mode = 'basic'
2025-08-02 22:03:38,091 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-08-02 22:03:38,091 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-08-02 22:03:38,091 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-08-02 22:03:38,091 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-08-02 22:03:38,092 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-08-02 22:03:38,092 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-08-02 22:03:38,092 - yfinance - DEBUG - reusing persistent cookie
2025-08-02 22:03:38,092 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-08-02 22:03:38,092 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-08-02 22:03:38,092 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-08-02 22:03:38,092 - yfinance - DEBUG - reusing cookie
2025-08-02 22:03:38,092 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-08-02 22:03:38,346 - yfinance - DEBUG - crumb = '8Iqp7FcY9f0'
2025-08-02 22:03:38,347 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-08-02 22:03:38,347 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-08-02 22:03:38,347 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-08-02 22:03:38,414 - yfinance - DEBUG - response code=200
2025-08-02 22:03:38,414 - yfinance - DEBUG -    Exiting _make_request()
2025-08-02 22:03:38,414 - yfinance - DEBUG -   Exiting get()
2025-08-02 22:03:38,420 - yfinance - DEBUG - BTC-USD: yfinance received OHLC data: 2025-07-03 00:00:00 -> 2025-08-02 14:02:00
2025-08-02 22:03:38,425 - yfinance - DEBUG - BTC-USD: OHLC after cleaning: 2025-07-03 00:00:00+00:00 -> 2025-08-02 14:02:00+00:00
2025-08-02 22:03:38,431 - yfinance - DEBUG - BTC-USD: OHLC after combining events: 2025-07-03 00:00:00+00:00 -> 2025-08-02 00:00:00+00:00
2025-08-02 22:03:38,436 - yfinance - DEBUG - BTC-USD: yfinance returning OHLC: 2025-07-03 00:00:00+00:00 -> 2025-08-02 00:00:00+00:00
2025-08-02 22:03:38,436 - yfinance - DEBUG -  Exiting history()
2025-08-02 22:03:38,436 - yfinance - DEBUG - Exiting history()
2025-08-02 22:03:38,436 - src.data.openbb_client - INFO - Successfully fetched 30 records
2025-08-02 22:03:38,436 - __main__ - INFO - ✅ Data loaded: 30 rows
2025-08-02 22:03:38,437 - __main__ - INFO -   Date range: 2025-07-03 00:00:00+00:00 to 2025-08-02 00:00:00+00:00
2025-08-02 22:03:38,437 - __main__ - INFO -   Columns: ['open', 'high', 'low', 'close', 'volume', 'Dividends', 'Stock Splits']
2025-08-02 22:03:38,437 - __main__ - INFO -   Price range: $108034.34 - $119995.41
2025-08-02 22:03:38,437 - __main__ - INFO - ✅ Data Loading: PASSED
2025-08-02 22:03:38,437 - __main__ - INFO - 
🧪 Running: Momentum Strategy
2025-08-02 22:03:38,437 - __main__ - INFO - ------------------------------
2025-08-02 22:03:38,437 - __main__ - INFO - === Testing Momentum Strategy ===
2025-08-02 22:03:38,437 - __main__ - INFO - Momentum strategy created
2025-08-02 22:03:38,442 - src.data.openbb_client - INFO - Fetching BTC-USD data from yfinance
2025-08-02 22:03:38,442 - yfinance - DEBUG - Entering history()
2025-08-02 22:03:38,443 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['BTC-USD', 1, 0])
2025-08-02 22:03:38,443 - yfinance - DEBUG -  Entering history()
2025-08-02 22:03:38,443 - yfinance - DEBUG - BTC-USD: Yahoo GET parameters: {'period1': '2025-07-03 22:03:38+00:00', 'period2': '2025-08-02 22:03:38+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-08-02 22:03:38,443 - yfinance - DEBUG -   Entering get()
2025-08-02 22:03:38,443 - yfinance - DEBUG -    Entering _make_request()
2025-08-02 22:03:38,443 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/BTC-USD
2025-08-02 22:03:38,443 - yfinance - DEBUG - params={'period1': 1751580218, 'period2': 1754172218, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-08-02 22:03:38,443 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-08-02 22:03:38,443 - yfinance - DEBUG - cookie_mode = 'basic'
2025-08-02 22:03:38,443 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-08-02 22:03:38,443 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-08-02 22:03:38,443 - yfinance - DEBUG - reusing cookie
2025-08-02 22:03:38,443 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-08-02 22:03:38,443 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-08-02 22:03:38,443 - yfinance - DEBUG - reusing crumb
2025-08-02 22:03:38,443 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-08-02 22:03:38,443 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-08-02 22:03:38,443 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-08-02 22:03:38,457 - yfinance - DEBUG - response code=200
2025-08-02 22:03:38,457 - yfinance - DEBUG -    Exiting _make_request()
2025-08-02 22:03:38,457 - yfinance - DEBUG -   Exiting get()
2025-08-02 22:03:38,458 - yfinance - DEBUG - BTC-USD: yfinance received OHLC data: 2025-07-03 00:00:00 -> 2025-08-02 14:02:00
2025-08-02 22:03:38,459 - yfinance - DEBUG - BTC-USD: OHLC after cleaning: 2025-07-03 00:00:00+00:00 -> 2025-08-02 14:02:00+00:00
2025-08-02 22:03:38,461 - yfinance - DEBUG - BTC-USD: OHLC after combining events: 2025-07-03 00:00:00+00:00 -> 2025-08-02 00:00:00+00:00
2025-08-02 22:03:38,463 - yfinance - DEBUG - BTC-USD: yfinance returning OHLC: 2025-07-03 00:00:00+00:00 -> 2025-08-02 00:00:00+00:00
2025-08-02 22:03:38,463 - yfinance - DEBUG -  Exiting history()
2025-08-02 22:03:38,463 - yfinance - DEBUG - Exiting history()
2025-08-02 22:03:38,464 - src.data.openbb_client - INFO - Successfully fetched 30 records
2025-08-02 22:03:38,466 - __main__ - INFO -   Signal 15: BUY (confidence: 1.000)
2025-08-02 22:03:38,467 - __main__ - INFO -   Signal 16: BUY (confidence: 1.000)
2025-08-02 22:03:38,467 - __main__ - INFO -   Signal 17: BUY (confidence: 1.000)
2025-08-02 22:03:38,469 - __main__ - INFO -   Signal 19: BUY (confidence: 1.000)
2025-08-02 22:03:38,470 - __main__ - INFO -   Signal 21: SELL (confidence: 1.000)
2025-08-02 22:03:38,472 - __main__ - INFO -   Signal 23: SELL (confidence: 0.666)
2025-08-02 22:03:38,473 - __main__ - INFO - ✅ Generated 6 signals successfully
2025-08-02 22:03:38,473 - __main__ - INFO - ✅ Momentum Strategy: PASSED
2025-08-02 22:03:38,473 - __main__ - INFO - 
🧪 Running: Simple Simulation
2025-08-02 22:03:38,473 - __main__ - INFO - ------------------------------
2025-08-02 22:03:38,473 - __main__ - INFO - === Running Simple Simulation ===
2025-08-02 22:03:38,473 - src.simulation.simulation_engine - INFO - SimulationEngine: Creating portfolio with $100,000.00
2025-08-02 22:03:38,473 - src.simulation.simulation_engine - INFO - SimulationEngine: Portfolio created with $100,000.00 cash
2025-08-02 22:03:38,473 - src.simulation.simulation_engine - INFO - Simulation engine initialized with $100,000.00 capital
2025-08-02 22:03:38,473 - __main__ - INFO - Engine initialized with $100,000.00
2025-08-02 22:03:38,478 - src.data.openbb_client - INFO - Fetching BTC-USD data from yfinance
2025-08-02 22:03:38,478 - yfinance - DEBUG - Entering history()
2025-08-02 22:03:38,478 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_tz_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['BTC-USD', 1, 0])
2025-08-02 22:03:38,478 - yfinance - DEBUG -  Entering history()
2025-08-02 22:03:38,478 - yfinance - DEBUG - BTC-USD: Yahoo GET parameters: {'period1': '2025-07-03 22:03:38+00:00', 'period2': '2025-08-02 22:03:38+00:00', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-08-02 22:03:38,478 - yfinance - DEBUG -   Entering get()
2025-08-02 22:03:38,478 - yfinance - DEBUG -    Entering _make_request()
2025-08-02 22:03:38,478 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/BTC-USD
2025-08-02 22:03:38,478 - yfinance - DEBUG - params={'period1': 1751580218, 'period2': 1754172218, 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-08-02 22:03:38,478 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-08-02 22:03:38,478 - yfinance - DEBUG - cookie_mode = 'basic'
2025-08-02 22:03:38,478 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-08-02 22:03:38,478 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-08-02 22:03:38,478 - yfinance - DEBUG - reusing cookie
2025-08-02 22:03:38,478 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-08-02 22:03:38,478 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-08-02 22:03:38,478 - yfinance - DEBUG - reusing crumb
2025-08-02 22:03:38,478 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-08-02 22:03:38,478 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-08-02 22:03:38,479 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-08-02 22:03:38,490 - yfinance - DEBUG - response code=200
2025-08-02 22:03:38,490 - yfinance - DEBUG -    Exiting _make_request()
2025-08-02 22:03:38,490 - yfinance - DEBUG -   Exiting get()
2025-08-02 22:03:38,491 - yfinance - DEBUG - BTC-USD: yfinance received OHLC data: 2025-07-03 00:00:00 -> 2025-08-02 14:02:00
2025-08-02 22:03:38,491 - yfinance - DEBUG - BTC-USD: OHLC after cleaning: 2025-07-03 00:00:00+00:00 -> 2025-08-02 14:02:00+00:00
2025-08-02 22:03:38,493 - yfinance - DEBUG - BTC-USD: OHLC after combining events: 2025-07-03 00:00:00+00:00 -> 2025-08-02 00:00:00+00:00
2025-08-02 22:03:38,495 - yfinance - DEBUG - BTC-USD: yfinance returning OHLC: 2025-07-03 00:00:00+00:00 -> 2025-08-02 00:00:00+00:00
2025-08-02 22:03:38,495 - yfinance - DEBUG -  Exiting history()
2025-08-02 22:03:38,495 - yfinance - DEBUG - Exiting history()
2025-08-02 22:03:38,495 - src.data.openbb_client - INFO - Successfully fetched 30 records
2025-08-02 22:03:38,495 - __main__ - INFO - Data loaded: 30 rows
2025-08-02 22:03:38,495 - src.simulation.simulation_engine - INFO - Loaded 30 data points from 2025-07-03 00:00:00+00:00 to 2025-08-02 00:00:00+00:00
2025-08-02 22:03:38,495 - src.simulation.simulation_engine - INFO - Strategy set: SimpleMomentumStrategy
2025-08-02 22:03:38,495 - __main__ - INFO - Before simulation - Cash: $100,000.00
2025-08-02 22:03:38,495 - __main__ - INFO - 
--- Step 1 (index 15) ---
2025-08-02 22:03:38,495 - __main__ - INFO - Price: $118003.23
2025-08-02 22:03:38,495 - __main__ - INFO - Cash before: $100000.00
2025-08-02 22:03:38,496 - __main__ - INFO - Signal: BUY (confidence: 1.000)
2025-08-02 22:03:38,496 - __main__ - INFO - Portfolio value: $100000.00
2025-08-02 22:03:38,496 - __main__ - INFO - Buy calculation: quantity=0.127115, cost=$15015.00
2025-08-02 22:03:38,496 - __main__ - INFO - Executing BUY order
2025-08-02 22:03:38,496 - __main__ - INFO - Cash after: $100000.00
2025-08-02 22:03:38,496 - __main__ - INFO - 
--- Step 2 (index 16) ---
2025-08-02 22:03:38,496 - __main__ - INFO - Price: $117939.98
2025-08-02 22:03:38,497 - __main__ - INFO - Cash before: $100000.00
2025-08-02 22:03:38,497 - __main__ - INFO - Signal: BUY (confidence: 1.000)
2025-08-02 22:03:38,497 - __main__ - INFO - Portfolio value: $100000.00
2025-08-02 22:03:38,497 - __main__ - INFO - Buy calculation: quantity=0.127183, cost=$15015.00
2025-08-02 22:03:38,497 - __main__ - INFO - Executing BUY order
2025-08-02 22:03:38,497 - __main__ - INFO - Cash after: $100000.00
2025-08-02 22:03:38,497 - __main__ - INFO - 
--- Step 3 (index 17) ---
2025-08-02 22:03:38,497 - __main__ - INFO - Price: $117300.79
2025-08-02 22:03:38,497 - __main__ - INFO - Cash before: $100000.00
2025-08-02 22:03:38,498 - __main__ - INFO - Signal: BUY (confidence: 1.000)
2025-08-02 22:03:38,498 - __main__ - INFO - Portfolio value: $100000.00
2025-08-02 22:03:38,498 - __main__ - INFO - Buy calculation: quantity=0.127876, cost=$15015.00
2025-08-02 22:03:38,498 - __main__ - INFO - Executing BUY order
2025-08-02 22:03:38,498 - __main__ - INFO - Cash after: $100000.00
2025-08-02 22:03:38,498 - __main__ - INFO - 
--- Step 4 (index 18) ---
2025-08-02 22:03:38,498 - __main__ - INFO - Price: $117439.54
2025-08-02 22:03:38,498 - __main__ - INFO - Cash before: $100000.00
2025-08-02 22:03:38,499 - __main__ - INFO - No signals generated
2025-08-02 22:03:38,499 - __main__ - INFO - 
--- Step 5 (index 19) ---
2025-08-02 22:03:38,499 - __main__ - INFO - Price: $119995.41
2025-08-02 22:03:38,499 - __main__ - INFO - Cash before: $100000.00
2025-08-02 22:03:38,499 - __main__ - INFO - Signal: BUY (confidence: 1.000)
2025-08-02 22:03:38,499 - __main__ - INFO - Portfolio value: $100000.00
2025-08-02 22:03:38,499 - __main__ - INFO - Buy calculation: quantity=0.125005, cost=$15015.00
2025-08-02 22:03:38,499 - __main__ - INFO - Executing BUY order
2025-08-02 22:03:38,499 - __main__ - INFO - Cash after: $100000.00
2025-08-02 22:03:38,499 - __main__ - INFO - ✅ Simple simulation completed
2025-08-02 22:03:38,500 - __main__ - INFO - ✅ Simple Simulation: PASSED
2025-08-02 22:03:38,500 - __main__ - INFO - 
==================================================
2025-08-02 22:03:38,500 - __main__ - INFO - 🎯 DEBUG SUMMARY
2025-08-02 22:03:38,500 - __main__ - INFO - ==================================================
2025-08-02 22:03:38,500 - __main__ - INFO - ✅ PASS: Portfolio Initialization
2025-08-02 22:03:38,500 - __main__ - INFO - ✅ PASS: Simulation Engine
2025-08-02 22:03:38,500 - __main__ - INFO - ✅ PASS: Data Loading
2025-08-02 22:03:38,500 - __main__ - INFO - ✅ PASS: Momentum Strategy
2025-08-02 22:03:38,500 - __main__ - INFO - ✅ PASS: Simple Simulation
2025-08-02 22:03:38,500 - __main__ - INFO - 
Overall: 5/5 tests passed
2025-08-02 22:03:38,500 - __main__ - INFO - 🎉 All tests passed! System is working correctly.
