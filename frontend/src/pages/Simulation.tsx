import React, { useState } from 'react';
import { SimulationConfig as SimulationConfigComponent } from '../components/simulation/SimulationConfig';
import { SimulationControls } from '../components/simulation/SimulationControls';
import { useSimulation } from '../hooks/useSimulation';
import { SimulationConfig as SimulationConfigType, StrategyParams } from '../types';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { PlayIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

export const Simulation: React.FC = () => {
  const {
    simulationState,
    createSimulation,
    startSimulation,
    pauseSimulation,
    stopSimulation,
    resetSimulation,
    isCreating,
    isStarting,
    isPausing,
    isStopping,
  } = useSimulation();

  const [config, setConfig] = useState<SimulationConfigType>({
    initial_capital: 100000,
    position_size: 0.2,
    commission_rate: 0.001,
    slippage_rate: 0.0005,
    max_positions: 3,
    speed_multiplier: 10,
  });

  const handleCreateAndStartSimulation = async () => {
    // Get strategy configuration from localStorage or use default
    const savedStrategy = localStorage.getItem('current-strategy');
    const savedParams = localStorage.getItem('current-strategy-params');

    if (!savedStrategy || !savedParams) {
      toast.error('Please configure a strategy first in the Strategy tab');
      return;
    }

    try {
      const strategyId = JSON.parse(savedStrategy);
      const strategyParams: StrategyParams = JSON.parse(savedParams);

      // Create simulation
      await createSimulation({
        strategyId,
        strategyParams,
        config,
      });

      // Start simulation automatically after creation
      setTimeout(() => {
        startSimulation();
      }, 1000);
    } catch (error) {
      console.error('Failed to create simulation:', error);
      toast.error('Failed to create simulation. Please check your configuration.');
    }
  };

  const handleExport = async () => {
    try {
      // This would call the export API
      toast.success('Export functionality coming soon!');
    } catch (error) {
      toast.error('Export failed');
    }
  };

  const hasActiveSimulation = simulationState && simulationState.status !== 'idle';

  return (
    <div className="space-y-6">
      {/* Simulation Configuration */}
      <SimulationConfigComponent
        config={config}
        onChange={setConfig}
      />

      {/* Simulation Controls */}
      <SimulationControls
        simulationState={simulationState}
        onStart={startSimulation}
        onPause={pauseSimulation}
        onStop={stopSimulation}
        onReset={resetSimulation}
        onExport={handleExport}
        isStarting={isStarting}
        isPausing={isPausing}
        isStopping={isStopping}
      />

      {/* Quick Start */}
      {!hasActiveSimulation && (
        <Card
          title="Quick Start"
          subtitle="Create and start a new simulation with current settings"
        >
          <div className="text-center space-y-4">
            <p className="text-gray-600 dark:text-gray-400">
              Ready to test your trading strategy? Click below to create and start a new simulation.
            </p>
            <Button
              variant="primary"
              size="lg"
              icon={<PlayIcon />}
              onClick={handleCreateAndStartSimulation}
              loading={isCreating}
              disabled={isCreating}
            >
              {isCreating ? 'Creating Simulation...' : 'Create & Start Simulation'}
            </Button>
          </div>
        </Card>
      )}

      {/* Simulation Status */}
      {hasActiveSimulation && (
        <Card title="Simulation Status">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400">Status</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white capitalize">
                {simulationState.status}
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400">Progress</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {(simulationState.progress * 100).toFixed(1)}%
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400">Current Date</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {simulationState.current_date 
                  ? new Date(simulationState.current_date).toLocaleDateString()
                  : 'N/A'
                }
              </p>
            </div>
          </div>
        </Card>
      )}

      {/* Configuration Summary */}
      <Card title="Current Configuration" subtitle="Review your simulation settings">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Initial Capital</p>
            <p className="text-lg font-semibold text-gray-900 dark:text-white">
              ${config.initial_capital.toLocaleString()}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Position Size</p>
            <p className="text-lg font-semibold text-gray-900 dark:text-white">
              {(config.position_size * 100).toFixed(1)}%
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Commission</p>
            <p className="text-lg font-semibold text-gray-900 dark:text-white">
              {(config.commission_rate * 100).toFixed(2)}%
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Speed</p>
            <p className="text-lg font-semibold text-gray-900 dark:text-white">
              {config.speed_multiplier}x
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
};
