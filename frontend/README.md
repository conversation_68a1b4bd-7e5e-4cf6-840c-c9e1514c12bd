# 🚀 Interactive Trading Simulator - React Frontend

A comprehensive, professional-grade React + TypeScript frontend application for the Interactive Trading Simulator, featuring real-time portfolio visualization, strategy customization, and performance analysis.

## ✨ Features

### 📊 **Core Visualization**
- **Real-time Portfolio Chart** - Live portfolio value tracking with performance metrics
- **Interactive Candlestick Chart** - BTC price data with trade entry/exit markers
- **Performance Metrics Dashboard** - Comprehensive cards showing returns, Sharpe ratio, drawdown
- **Trade History Table** - Sortable, filterable trade execution history
- **Strategy Comparison** - Side-by-side performance analysis

### ⚙️ **Strategy Customization**
- **6 Trading Strategies** with detailed descriptions:
  - 🏆 **Balanced Ensemble (CHAMPION)** - Confidence-weighted multi-strategy approach
  - 🛡️ **Conservative Ensemble** - High consensus requirements
  - ⚡ **Aggressive Ensemble** - Low consensus for frequent trading
  - 📈 **MA Crossover Only** - Moving average trend following
  - 🔄 **RSI Mean Reversion Only** - Range-bound market strategy
  - 🚀 **Momentum Only** - Pure trend momentum strategy

### 🎮 **Simulation Management**
- **Interactive Controls** - Play/pause/stop/reset simulation
- **Real-time Progress** - Live progress bar and status updates
- **Configuration Panel** - Capital, position size, commission, slippage settings
- **Speed Control** - 1x to 50x simulation speed multiplier
- **Export Functionality** - CSV/PDF export of results

### 🎨 **User Experience**
- **Responsive Design** - Works on desktop and tablet
- **Dark/Light Theme** - Automatic system preference detection
- **Save/Load Configurations** - LocalStorage persistence
- **Professional Tooltips** - Explanations for all financial terms
- **Form Validation** - Real-time validation with helpful error messages

## 🏗️ **Technical Stack**

- **React 18+** with TypeScript for type safety
- **Tailwind CSS** for responsive styling
- **Framer Motion** for smooth animations
- **React Hook Form** for efficient form management
- **React Query** for data fetching and caching
- **Recharts** for interactive data visualizations
- **Headless UI** for accessible components

## 🚀 **Quick Start**

### **Prerequisites**
- Node.js 16+ and npm/yarn

### **Installation**
```bash
cd frontend
npm install
npm start
```

### **Access the Application**
- Frontend: http://localhost:3000
- Ensure backend is running on http://localhost:8000

## 📱 **Application Structure**

### **Main Tabs**
1. **Dashboard** - View simulation results and performance metrics
2. **Strategy** - Configure trading strategy parameters
3. **Simulation** - Run and control trading simulations
4. **Analysis** - Detailed performance analysis (coming soon)
5. **Saved Configs** - Manage saved strategy configurations

### **Key Components**
- `PortfolioChart.tsx` - Real-time portfolio value tracking
- `CandlestickChart.tsx` - Price data with trade markers
- `PerformanceMetrics.tsx` - Comprehensive metrics dashboard
- `StrategySelector.tsx` - Strategy selection with descriptions
- `SimulationControls.tsx` - Simulation management interface

## 🔧 **Configuration**

### **Environment Variables**
Create a `.env` file in the frontend directory:
```
REACT_APP_API_URL=http://localhost:8000/api
```

### **Strategy Parameters Example**
```typescript
{
  ensemble_method: "confidence_weighted",
  min_consensus: 0.4,
  confidence_threshold: 0.3,
  strategy_weights: {
    ma_crossover: 0.33,
    rsi_mean_reversion: 0.33,
    momentum: 0.34
  }
}
```

## 📊 **Performance Metrics**

The application displays comprehensive trading performance metrics:

- **Total Return** - Portfolio gain/loss percentage
- **Sharpe Ratio** - Risk-adjusted return measure
- **Maximum Drawdown** - Largest peak-to-trough decline
- **Volatility** - Standard deviation of returns
- **Win Rate** - Percentage of profitable trades
- **Profit Factor** - Ratio of gross profit to gross loss

## 🎨 **Design System**

### **Color Palette**
- **Primary**: Blue (#3b82f6) for main actions
- **Success**: Green (#22c55e) for positive metrics
- **Danger**: Red (#ef4444) for negative metrics
- **Warning**: Yellow (#f59e0b) for neutral alerts

### **Typography**
- **Font**: Inter (Google Fonts)
- **Responsive sizing** with Tailwind utilities

## 🚀 **Deployment**

```bash
npm run build
# Deploy build/ directory to your hosting provider
```

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Open a Pull Request

---

**Built with ❤️ for professional cryptocurrency trading strategy analysis.**
